<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.nodeID')" prop="nodeId">
        <el-select class="m-2" v-model="dataForm.nodeId" value-key="id" :placeholder="$t('edge.selectModel')">
          <el-option v-for="item in nodesInfo" :key="item.id" :label="item.sname" :value="item.saddress" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('edge.linkID')" prop="linkId">
        <el-select class="m-2" v-model="dataForm.linkId" value-key="id" :placeholder="$t('edge.selectLink')" @change="onSelectLinks">
          <el-option v-for="item in linksInfo" :key="item.id" :label="item.linkName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('edge.linkName')" prop="linkName">
        <el-input v-model="dataForm.linkName" :placeholder="$t('edge.linkName')"></el-input>
      </el-form-item>

      <el-form-item label="数据推送URL" prop="configs.sendUrl">
        <el-select v-model="dataForm.configs.send_url" class="m-2" value-key="id" :placeholder="$t('edge.selectPush')"
                   @change="onSelectPush">
          <el-option v-for="item in pushInfo" :key="item.id" :label="item.sname" :value="item.id"/>
        </el-select>
        <el-input v-model="dataForm.configs.send_url" placeholder="数据推送URL"></el-input>
      </el-form-item>
      <!-- <el-form-item label="推送地址" prop="configs.videoInput">
        <el-input v-model="dataForm.configs.video_input" placeholder="推送地址"></el-input>
      </el-form-item>  -->

      <el-form-item label="推送地址" prop="configs.videoInput">
        <el-select class="m-2" v-model="dataForm.configs.video_input" value-key="id" :placeholder="$t('edge.selectModel')"
                   @change="onSelectCamera">
          <el-option v-for="item in cameraInfo" :key="item.id" :label="item.name" :value="item.uuid"/>
        </el-select>
        <el-input v-model="dataForm.configs.video_input" placeholder="推送地址"></el-input>
      </el-form-item>
      <!-- <el-form-item :label="$t('edge.parameterConfiguration')" prop="configs">
        <el-input v-model="dataForm.configs" @input="changeTextarea" :autosize="{ minRows: 4, maxRows: 10 }" type="textarea" :placeholder="$t('edge.parameterConfiguration')"> </el-input>
      </el-form-item> -->
      <!-- <el-form-item :label="$t('edge.parameterConfigurationInfo')" prop="configs">
        <pre class="language-javascript"><code>{{ code }}</code></pre>
      </el-form-item> -->
      <el-popover
        placement="bottom-start"
        title=""

        trigger="click"
        :popper-style="popoverStyles"
      >
        <template #reference>
          <el-button class="m-2s">更多</el-button>
        </template>

                <el-form-item label="阈值" prop="configs.threshold">
                  <el-input v-model="dataForm.configs.threshold" placeholder="阈值"></el-input>
                </el-form-item>

                <el-form-item label="时间间隔 10 帧" prop="configs.timeInter">
                  <el-input v-model="dataForm.configs.time_inter" placeholder="时间间隔 10 帧"></el-input>
                </el-form-item>
                <el-form-item label="分析帧30" prop="configs.analysisFrame">
                  <el-input v-model="dataForm.configs.analysis_frame" placeholder="分析帧30"></el-input>
                </el-form-item>
                <el-form-item label="设备ID" prop="configs.deviceId">
                  <el-input v-model="dataForm.configs.device_id" placeholder="设备ID"></el-input>
                </el-form-item>
                <el-form-item label="摄像头ID" prop="configs.cameraId">
                  <el-input v-model="dataForm.configs.camera_id" placeholder="摄像头ID"></el-input>
                </el-form-item>

                <el-form-item label="摄像头位置信息" prop="configs.cameraLocal">
                  <el-input v-model="dataForm.configs.camera_local" placeholder="摄像头位置信息"></el-input>
                </el-form-item>
                <el-form-item label="感兴趣区域坐标" prop="configs.roi">
                  <el-input v-model="dataForm.configs.roi" placeholder="感兴趣区域坐标"></el-input>
                </el-form-item>
                </el-popover>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import {Camerainfo, ImagesInfo, Modelinfo, PushInfo, TrainInfo, TrainModelInfo} from "@/types/interface";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
import { NodesOrLinks } from "@/types/interface";
import Prism from "prismjs";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const pushInfo = ref<PushInfo[]>([]); // 推送列表
const cameraInfo = ref<Camerainfo[]>([]); // 摄像头列表
const visible = ref(false);
const code = ref(null);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  nodeId: "",
  linkId: "",
  linkName: "",
  // sendUrl: '',
  configs: {
    send_url: "",
    video_input: "",
    threshold: 0.3,
    time_inter: 10,
    analysis_frame: 30,
    device_id: "EM01",
    camera_id: "003001",
    camera_local: "['xzu','91.18577','29.64932']",
    roi: "[92,101,1874,118,1844,1044,140,952]"
}
});
const nodesInfo = ref<NodesOrLinks[]>([]);
const linksInfo = ref<NodesOrLinks[]>([]);
const rules = ref({
  nodeId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
  linkId: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
});
// 选择推送地址
const onSelectPush = (value: any) => {
  let it = pushInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.sprotoName) {
    dataForm.configs.send_url = it.sprotoName || '';
  }
};
// 选择摄像头
const onSelectCamera = (value: any) => {
  let it = cameraInfo.value.find((item) => {
    return (item.uuid == value);
  });
  if (it?.name) {
    dataForm.configs.video_input = it.streamUrl || '';
    // dataForm.videoName = it.name;
  }
};
const initForm = initDataForm(dataForm);
const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  code.value = null
  Promise.all([

    baseService.get("/edge/tnodeinfo/page"), // 加载node列表
    baseService.get("/edge/tlinkinfo/page"),// 加载links列表
    baseService.get("/edge/tcamerainfo/AllInfoList"), //加载摄像头列表
  ]).then(([nodeInfo, linkInfo ,tcamerainfo]) => {
    nodesInfo.value = nodeInfo.data.list;
    linksInfo.value = linkInfo.data.list;
    cameraInfo.value = tcamerainfo.data;
  });
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }

  if (id) {
    getInfo(id);
  }
};
const onSelectLinks = (value: string | number) => {
  let it = linksInfo.value.find((item) => {
    return item.id === value;
  });
  if (it?.linkName) {
    dataForm.linkName = it.linkName;
  }
};
// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/tpushinfo/page"), //加载推送列表
  ]).then(([ tpushinfo]) => {
    pushInfo.value = tpushinfo.data.list;

  });
};
getAllInfo();
// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/edge/tdeployinfo/${id}`).then((res: any) => {
    code.value = res.data.configs;
    Object.assign(dataForm, res.data);
    // dataForm.configs = JSON.stringify(res.data.configs,null,4);
  }); // 加载摄像头列表
  Prism.highlightAll(
    Prism.languages.javascript, // 使用 JavaScript 语法高亮
    "javascript"
  );
};
const popoverStyles =  {
        backgroundColor: '#f5f5f5',
        width: '45vw'
      }
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    // if (dataForm.configs !== null && dataForm.configs !== "") {
      try {
        // dataForm.configs = JSON.parse(dataForm.configs.trim());
      } catch (e) {
        console.log(dataForm);

        ElMessage({
          type: "error",
          message: $t("validate.format")
        });
        return false;
      }
    // }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tdeployinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};
console.log(dataForm);

const changeTextarea = (value: string) => {
  try {
   code.value = JSON.parse(value.trim())
  } catch (error) {
    ElMessage({
      type: "error",
      message: $t('edge.parameterConfigurationConfirm')
    });
  }
};

defineExpose({
  init
});
</script>
<style scoped>
.el-button + .el-button {
  margin-left: 8px;
}
.m-2s{
  margin-left: 20px;
}
</style>
