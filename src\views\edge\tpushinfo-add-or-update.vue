<template>
  <el-dialog align-center v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.pushName')" prop="sname">
        <el-input v-model="dataForm.sname" :placeholder="$t('edge.pushName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.pushURL')" prop="sprotoName">
        <el-input v-model="dataForm.sprotoName" :placeholder="$t('edge.pushURL')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.username')" prop="susername">
        <el-input v-model="dataForm.susername" :placeholder="$t('edge.username')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.password')" prop="spassword">
        <el-input v-model="dataForm.spassword" :placeholder="$t('edge.password')"></el-input>
      </el-form-item>
      <el-form-item label="token" prop="stoken">
        <el-input v-model="dataForm.stoken" placeholder="token"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  sname: "",
  sprotoName: "",
  susername: "",
  spassword: "",
  stoken: ""
});
const initForm = initDataForm(dataForm);
const rules = ref({
  sname: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sprotoName: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});
const init = (id?: string) => {
  visible.value = true;
  dataForm.id = "";
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }
  if (id) {
    getInfo(id);
  }
};
// 获取信息
const getInfo = (id: string) => {
  baseService.get("/edge/tpushinfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
  });
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tpushinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
