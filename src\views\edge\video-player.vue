<template>
  <!-- Your existing template code -->
  <!-- Add a modal or component for video player -->
  <div v-if="showVideoPlayer">
    <video ref="videoPlayer" controls autoplay></video>
  </div>
</template>

<script lang="ts" setup>
// Import necessary libraries
import { ref } from "vue";

// Define a reactive variable to control visibility of video player
const showVideoPlayer = ref(false);

// Define a method to play RTSP stream
const onPlayVideo = (streamUrl: string) => {
  // Show the video player
  showVideoPlayer.value = true;

  // Get the video player element
  const videoElement = document.querySelector('video');

  // Set the RTSP stream URL as the source
  if (videoElement) {
    videoElement.src = streamUrl;
  }
};
</script>
