<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="主机名" prop="hostname">
        <el-input v-model="dataForm.hostname" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="hostId">
        <el-input v-model="dataForm.hostId" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="os">
        <el-input v-model="dataForm.os" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="platform">
        <el-input v-model="dataForm.platform" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="platformFamily">
        <el-input v-model="dataForm.platformFamily" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="platformVersion">
        <el-input v-model="dataForm.platformVersion" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="kernelArch">
        <el-input v-model="dataForm.kernelArch" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="kernelVersion">
        <el-input v-model="dataForm.kernelVersion" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ipv4Addr">
        <el-input v-model="dataForm.ipv4Addr" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="systemProxy">
        <el-input v-model="dataForm.systemProxy" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuCores">
        <el-input v-model="dataForm.cpuCores" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuLogicalCores">
        <el-input v-model="dataForm.cpuLogicalCores" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuModelName">
        <el-input v-model="dataForm.cpuModelName" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="uptime">
        <el-input v-model="dataForm.uptime" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="timeSinceUptime">
        <el-input v-model="dataForm.timeSinceUptime" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="procs">
        <el-input v-model="dataForm.procs" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="load1">
        <el-input v-model="dataForm.load1" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="load5">
        <el-input v-model="dataForm.load5" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="load15">
        <el-input v-model="dataForm.load15" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="loadUsagePercent">
        <el-input v-model="dataForm.loadUsagePercent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuUsedPercent">
        <el-input v-model="dataForm.cpuUsedPercent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuUsed">
        <el-input v-model="dataForm.cpuUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="cpuTotal">
        <el-input v-model="dataForm.cpuTotal" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryTotal">
        <el-input v-model="dataForm.memoryTotal" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryAvailable">
        <el-input v-model="dataForm.memoryAvailable" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryUsed">
        <el-input v-model="dataForm.memoryUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryUsedPercent">
        <el-input v-model="dataForm.memoryUsedPercent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="swapTotal">
        <el-input v-model="dataForm.swapTotal" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="swapAvailable">
        <el-input v-model="dataForm.swapAvailable" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="swapUsed">
        <el-input v-model="dataForm.swapUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="swapUsedPercent">
        <el-input v-model="dataForm.swapUsedPercent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ioReadBytes">
        <el-input v-model="dataForm.ioReadBytes" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ioWriteBytes">
        <el-input v-model="dataForm.ioWriteBytes" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ioCount">
        <el-input v-model="dataForm.ioCount" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ioReadTime">
        <el-input v-model="dataForm.ioReadTime" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="ioWriteTime">
        <el-input v-model="dataForm.ioWriteTime" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="netBytesSent">
        <el-input v-model="dataForm.netBytesSent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="netBytesRecv">
        <el-input v-model="dataForm.netBytesRecv" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="shotTime">
        <el-input v-model="dataForm.shotTime" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="createdAt">
        <el-input v-model="dataForm.createdAt" placeholder=""></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  hostname: '',  hostId: '',  os: '',  platform: '',  platformFamily: '',  platformVersion: '',  kernelArch: '',  kernelVersion: '',  ipv4Addr: '',  systemProxy: '',  cpuCores: '',  cpuLogicalCores: '',  cpuModelName: '',  uptime: '',  timeSinceUptime: '',  procs: '',  load1: '',  load5: '',  load15: '',  loadUsagePercent: '',  cpuUsedPercent: '',  cpuUsed: '',  cpuTotal: '',  memoryTotal: '',  memoryAvailable: '',  memoryUsed: '',  memoryUsedPercent: '',  swapTotal: '',  swapAvailable: '',  swapUsed: '',  swapUsedPercent: '',  ioReadBytes: '',  ioWriteBytes: '',  ioCount: '',  ioReadTime: '',  ioWriteTime: '',  netBytesSent: '',  netBytesRecv: '',  shotTime: '',  createdAt: ''});

const rules = ref({
          hostname: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          hostId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          os: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          platform: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          platformFamily: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          platformVersion: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          kernelArch: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          kernelVersion: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ipv4Addr: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          systemProxy: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuCores: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuLogicalCores: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuModelName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          uptime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          timeSinceUptime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          procs: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          load1: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          load5: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          load15: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          loadUsagePercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuUsedPercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cpuTotal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryTotal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryAvailable: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryUsedPercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          swapTotal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          swapAvailable: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          swapUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          swapUsedPercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ioReadBytes: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ioWriteBytes: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ioCount: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ioReadTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          ioWriteTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          netBytesSent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          netBytesRecv: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          shotTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createdAt: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/dashboardsnapshot/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/dashboardsnapshot", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
