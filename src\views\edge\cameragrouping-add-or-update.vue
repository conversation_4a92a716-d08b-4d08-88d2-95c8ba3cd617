<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="父分组ID" prop="parentId">
        <el-select v-model="dataForm.parentId" placeholder="选择父分组" clearable filterable style="width: 100%;">
          <el-option label="无父分组" :value="null"></el-option>
          <el-option
            v-for="item in parentGroupOptions"
            :key="item.id"
            :label="item.gName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分组名称" prop="gName">
        <el-input v-model="dataForm.gName" placeholder="摄像头分组名称"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input v-model="dataForm.memo" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { safeApiCall, getCameraGrouping } from "@/utils/api";
import type { AxiosResponse } from 'axios'; // 假设 http 返回 AxiosResponse

// 后端业务数据响应结构
interface BackendResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

// loadAllGroups 中 getCameraGrouping 成功时 res.data.data 的类型
interface GroupListItem { id: string; gName: string }

// safeApiCall 错误时返回的类型
interface SafeCallError {
  code: -1;
  data: null;
  msg: string;
}

const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  parentId: null as string | null, 
  gName: "",
  memo: ""
});

const rules = ref({
  parentId: [], 
  gName: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const allGroups = ref<Array<GroupListItem>>([]);

const parentGroupOptions = computed(() => {
  if (dataForm.id) {
    return allGroups.value.filter(group => group.id !== dataForm.id);
  }
  return allGroups.value;
});

const loadAllGroups = async () => {
  const result = await safeApiCall(getCameraGrouping);

  // 检查 result 是否是 safeApiCall 捕获的错误
  if (result && (result as SafeCallError).code === -1) {
    allGroups.value = [];
    ElMessage.error("加载父分组列表失败: " + (result as SafeCallError).msg);
    console.error("safeApiCall 捕获到API调用错误: ", result);
    return;
  }

  // 如果不是 safeApiCall 的错误，则 result 是 getCameraGrouping 的成功响应 (AxiosResponse)
  // 我们需要检查 HTTP 响应本身，并获取其 data 部分 (即 BackendResponse)
  const axiosResponse = result as AxiosResponse<BackendResponse<GroupListItem[]>>;

  if (axiosResponse && axiosResponse.data) {
    const backendData = axiosResponse.data;
    if (backendData.code === 0) {
      allGroups.value = backendData.data || [];
    } else {
      allGroups.value = [];
      ElMessage.error("加载父分组列表失败: " + (backendData.msg || '请求处理失败'));
      console.error("加载父分组列表API响应业务码非0: ", backendData);
    }
  } else {
    allGroups.value = [];
    ElMessage.error("加载父分组列表失败: 响应格式不正确或意外错误");
    console.error("loadAllGroups 收到的响应格式不符合预期: ", axiosResponse);
  }
};

const init = async (id?: number) => {
  visible.value = true;
  dataForm.id = ""; 

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  
  await loadAllGroups(); 

  if (id) {
    dataForm.id = id.toString(); 
    getInfo(id);
  }
};

const getInfo = (id: number) => {
  baseService.get("/edge/cameragrouping/" + id).then((res: BackendResponse<typeof dataForm>) => {
    if (res.code === 0) {
      Object.assign(dataForm, res.data);
      if (res.data.parentId === "" || res.data.parentId === (0 as any) || res.data.parentId === undefined) {
        dataForm.parentId = null;
      }
    } else {
      ElMessage.error("获取分组信息失败: " + res.msg);
    }
  });
};

const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/cameragrouping", dataForm).then((res: BackendResponse) => {
      if (res.code === 0) {
        ElMessage.success({
          message: res.msg || "操作成功",
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      } else {
        ElMessage.error(res.msg || "操作失败");
      }
    });
  });
};

defineExpose({
  init
});
</script>
