<template>
  <div class="rr-login">
    <video style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;"
      src="@/assets/images/bac.mp4" autoplay loop muted disablePictureInPicture></video>
    <div class="rr-login-wrap">


      


        <div class="rr-login-form-container">
          <el-form class="elform" ref="formRef" :status-icon="true" :model="login" :rules="rules"
            @keyup.enter="onLogin">
            <h1>{{ sysName }}</h1>
            <el-form-item label-width="0" prop="username">
              <el-input v-model="login.username" placeholder="请输入您的用户名" prefix-icon="user" autocomplete="off"
                style="width: 300px; margin: 0 auto;"></el-input>
            </el-form-item>
            <el-form-item label-width="0" prop="password">
              <el-input autocomplete="new-password" placeholder="请输入您的密码" v-model="login.password" prefix-icon="lock"
                show-password style="width: 300px; margin: 0 auto;"></el-input>
            </el-form-item>
            <el-form-item label-width="0" prop="captcha">
              <el-space class="rr-login-right-main-code">
                <el-input class="captcha" v-model="login.captcha" placeholder="验证码" prefix-icon="first-aid-kit"
                  style="width: 120px; margin: 0 0 0 50px;"></el-input>
                <img class="captcha-img" :src="state.captchaUrl" @click="onRefreshCode" alt="" />
              </el-space>
            </el-form-item>
            <el-form-item label-width="0" class="btn-container">
              <el-button type="primary" :disabled="state.loading" @click="onLogin" class="login-btn"
                style="margin: 0 auto">登录</el-button>
            </el-form-item>
          </el-form>

        </div>
      
    </div>
    <div class="login-footer">
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, onBeforeMount } from "vue";
import { CacheToken } from "@/constants/cacheKey";
import baseService from "@/service/baseService";
import { setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";

const store = useAppStore();
const router = useRouter();

const state = reactive({
  captchaUrl: "",
  loading: false,
  year: new Date().getFullYear()
});

const login = reactive({ username: "", password: "", captcha: "", uuid: "" });

const sysName = ref("")

onBeforeMount(() => {
  baseService.get("/sys/customization/newest").then((res) => {
    sysName.value = res.data.sysName
  });
})

onMounted(() => {
  //清理数据
  store.logout();
  getCaptchaUrl();
});
const formRef = ref();

const rules = ref({
  username: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  password: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  captcha: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const getCaptchaUrl = () => {
  login.uuid = getUuid();
  state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
};

const onRefreshCode = () => {
  getCaptchaUrl();
};

const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            setCache(CacheToken, res.data, true);
            ElMessage.success("登录成功");
            router.push("/");
          } else {
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          onRefreshCode();
        });
    }
  });
};
</script>

<style lang="less" scoped>

.rr-login {
  width: 100vw;
  height: 100vh;

  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

h1 {
    text-align: center;
    color: white;
    font-size: 36px;
    margin-bottom: 30px;
  }

    .rr-login-form-container {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 8px;
      padding: 30px;
      width: 460px;
    }
    .elform {
      width: 100%;
      margin: 0 auto;
    }
  
    .el-input {
      --el-input-border-radius: 20px;
      --el-input-height: 40px;
      --el-input-focus-border-color: #409EFF;
      margin-bottom: 15px;
  
  
      :deep(.el-input__wrapper) {
        border-radius: 20px;
      }
    }
  
    .captcha {
      width: 240px;
    }
  
    .captcha-img {
      height: 40px;
      border-radius: 4px;
      margin-left: 20px;
    }
  
    .btn-container {
      display: flex;
      justify-content: center;
    }
  
    .login-btn {
      width: 200px;
      height: 40px;
      font-size: 16px;
      border-radius: 20px;
      margin-top: 10px;
    }
  
}
</style>
