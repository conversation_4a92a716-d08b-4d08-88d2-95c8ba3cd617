<template>
  <div id="devicePlayer">
    <el-dialog
      title="视频播放"
      top="0"
      :close-on-click-modal="false"
      v-model="showVideoDialog"
      :loading="closeLoading"
      @close="close"
      style="width: 100vh"
    >
      <div style="width: 100%; height: 100%">
        <el-tabs type="card" stretch v-model="activePlayer">
          <el-tab-pane label="纯H5低延迟直播流播放器" name="jessibuca">
            <jessibuca-player v-if="activePlayer === 'jessibuca'" ref="jessibuca" v-model="showVideoDialog"
                              :video-url="videoUrl" @error="videoError" style="height: 60vh"
                              :has-audio="hasAudio" fluent autoplay live></jessibuca-player>
          </el-tab-pane>
          <!-- <el-tab-pane label="WebRTC Streamer" name="webRTCStreamer"> -->
            <!-- Add WebRTC Streamer player if required -->
          <!-- </el-tab-pane> -->
        </el-tabs>
      </div>
      <div id="shared" style="text-align: right; margin-top: 1rem;">
        <el-tabs v-model="tabActiveName">
          <el-tab-pane label="实时视频" name="media">
            <div style="display: flex; margin-bottom: 0.5rem; height: 2.5rem;">
              <span style="width: 8rem; line-height: 2.5rem; text-align: right;">播放地址：</span>
              <el-input v-model="videoUrl" :disabled="true">
              </el-input>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ref, onMounted } from 'vue';
import JessibucaPlayer from './jessibuca.vue';
import axios from 'axios';
import app from "@/constants/app";

export default {
  components: {
    JessibucaPlayer,
  },
  setup() {
    const player = ref({ jessibuca: ["ws_flv", "wss_flv"] });
    const showVideoDialog = ref(false);
    const activePlayer = ref("jessibuca");
    const videoUrl = ref('');
    const tabActiveName = ref('media');
    const streamId = ref('');
    const mediaServerId = ref('');
    const streamInfo = ref(null);
    const showPtz = ref(true);
    const showRecord = ref(true);
    const hasAudio = ref(false);
    const coverPlaying = ref(false);
    const closeLoading = ref(false);

    onMounted(() => {
      console.log("mounted");
      console.log(player.value);
      if (Object.keys(player.value).length === 1) {
        activePlayer.value = Object.keys(player.value)[0];
      }
    });

    const openDialog = (tab: string, param: any) => {
      if (showVideoDialog.value) {
        return;
      }
      tabActiveName.value = tab;
      streamId.value = "";
      mediaServerId.value = "";
      videoUrl.value = "";
      switch (tab) {
        case "media":
          play(param.streamInfo, param.hasAudio);
          break;
        case "streamPlay":
          tabActiveName.value = "media";
          showRecord.value = false;
          showPtz.value = false;
          play(param.streamInfo, param.hasAudio);
          break;
        case "control":
          break;
      }
    };

    const play = (streamInfoValue: any, hasAudioValue: boolean) => {
      streamInfo.value = streamInfoValue;
      hasAudio.value = hasAudioValue;
      videoUrl.value = `${streamInfoValue.deviceID}${streamInfoValue.channelId}.live.flv`;
      playFromStreamInfo(false);
    };

    const playFromStreamInfo = (realHasAudio: boolean) => {
      showVideoDialog.value = true;
      hasAudio.value = realHasAudio && hasAudio.value;
    };

    const close = () => {
      console.log('关闭视频');
      closeLoading.value = true;
      axios.post('/index/api/close_streams', {
        secret: app.media_secret,
        force: true,
      }).then((res) => {
        let isClosed = res.data.count_closed !== 0;
        console.log(isClosed ? "关了" : "没关");
        closeLoading.value = false;
        videoUrl.value = '';
        showVideoDialog.value = false;
      });
    };

    const videoError = (e: Event) => {
      console.log("播放器错误：" + JSON.stringify(e));
    };

    return {
      openDialog,
      close,
      videoError,
      activePlayer,
      videoUrl,
      tabActiveName,
      hasAudio,
      closeLoading,
      showVideoDialog,
    };
  }
}
</script>

<style scoped>

.control-btn i {
  font-size: 20px;
  color: #78aee4;
  display: flex;
  justify-content: center;
  align-items: center;
}

.control-btn i:hover {
  cursor: pointer
}

.control-top i {
  transform: rotate(45deg);
  border-radius: 5px 100% 5px 0;
}

.control-top .control-inner {
  left: -1px;
  bottom: 0;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.control-top .fa {
  transform: rotate(45deg) translateY(-7px);
}

.control-left i {
  transform: rotate(-45deg);
}

.control-left .control-inner {
  right: -1px;
  top: -1px;
  border-bottom: 1px solid #78aee4;
  border-left: 1px solid #78aee4;
  border-radius: 0 0 0 100%;
}

.control-left .fa {
  transform: rotate(-45deg) translateX(-7px);
}

.control-right i {
  transform: rotate(-45deg);
}

.control-right .control-inner {
  left: -1px;
  bottom: -1px;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 100% 0 0;
}

.control-right .fa {
  transform: rotate(-45deg) translateX(7px);
}

.control-bottom i {
  transform: rotate(-45deg);
}

.control-bottom .control-inner {
  top: -1px;
  left: -1px;
  border-bottom: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 0 100% 0;
}

.control-bottom .fa {
  transform: rotate(-45deg) translateY(7px);
}

</style>
