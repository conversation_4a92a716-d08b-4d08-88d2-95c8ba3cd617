<template>
  <div class="mod-edge__trainmodel">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border :show-overflow-tooltip="true" @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="modelName" label="模型训练名称" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="modelType" label="模型类型(yolov8,yolov9)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="modelBaseName" label="基础模型名称" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column prop="modelBaseUrl" label="基础模型url 地址" header-align="center" align="center"></el-table-column>
      <el-table-column prop="datasetsId" label="数据集id" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="datasetsName" label="数据集名称" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="datasetsUrl" label="数据集URL" header-align="center" align="center" show-overflow-tooltip></el-table-column> -->
      <!-- <el-table-column prop="cmdTrain" label="执行命令行" header-align="center" align="center" show-overflow-tooltip></el-table-column> -->
      <!-- <el-table-column prop="configs" label="参数配置" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="modelAfterTrainingUrl" label="训练后的模型" header-align="center" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="nodeId" :label="$t('edge.nodeID')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="logs" label="训练日志" header-align="center" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="memo" label="备注" header-align="center" align="center"></el-table-column>
      <el-table-column prop="twfCreated" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="twfModified" label="更新时间" header-align="center" align="center"></el-table-column> -->
      <!--              <el-table-column prop="twfDeleted" label="删除时间" header-align="center" align="center"></el-table-column>-->
      <el-table-column label="查看详情" header-align="center" align="center" width="150px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="details(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="warning" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>

  <el-dialog v-model="dialogTableVisible" title="模型训练详情" width="800" style="height: 470px; overflow: auto">
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100" style="height: 100px; text-align: center" v-for="item in gridData" :key="item.id">
      <tr>
        <td style="color: #327AE6; font-weight: bold">ID</td>
        <td>{{ item.id }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">模型训练名称</td>
        <td>{{ item.modelName }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">模型类型(yolov8,yolov9)</td>
        <td>{{ item.modelType }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">基础模型名称</td>
        <td>{{ item.modelBaseName }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">基础模型url 地址</td>
        <td>{{ item.modelBaseUrl }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">数据集id</td>
        <td>{{ item.datasetsId }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">数据集名称</td>
        <td>{{ item.datasetsName }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">数据集URL</td>
        <td>{{ item.datasetsUrl }}</td>
        
      </tr>

    </table>

    <br />
  </el-dialog>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onBeforeUnmount, onMounted, reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./trainmodel-add-or-update.vue";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import router from "@/router";
import ExportButton from "@/components/export-button/index.vue";

//根据id获取详情
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const dialogTableVisible = ref(false);
const gridData = ref();
//查看详情
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : []; // 包装在数组中
  dialogTableVisible.value = true;
};
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/trainmodel/page",
  getDataListIsPage: true,
  exportURL: "/edge/trainmodel/export",
  deleteURL: "/edge/trainmodel"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// const timeLeft = ref(5); // 倒计时时间（秒）
// let timer: number | undefined;
//
// const startTimer = () => {
//   timer = window.setInterval(() => {
//     if (timeLeft.value > 0) {
//       timeLeft.value--;
//     } else {
//       window.location.reload(); // 刷新页面
//     }
//   }, 1000); // 每秒更新一次
// };
//
// onMounted(() => {
//   startTimer();
// });
//
// onBeforeUnmount(() => {
//   if (timer) {
//     clearInterval(timer);
//   }
// });
</script>
<style scoped>
table {
  width: 100%; /* 设置表格宽度为100% */
  max-width: 1200px; /* 设置表格的最大宽度 */
  border-collapse: collapse; /* 使用合并边框 */
  table-layout: fixed; /* 固定布局 */
}
th,
td {
  padding: 10px; /* 单元格内边距 */
  overflow: auto; /* 溢出隐藏 */
  white-space: nowrap; /* 不换行 */
}
</style>