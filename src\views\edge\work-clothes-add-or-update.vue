<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="工服名称" prop="workClothesName">
        <el-input v-model="dataForm.workClothesName" placeholder="工服名称"></el-input>
      </el-form-item>
      <el-form-item label="工服类型" prop="workClothesType">
        <el-input v-model="dataForm.workClothesType" placeholder="工服类型"></el-input>
      </el-form-item>
      <el-form-item label="工服图片" prop="workClothesImages">
        <el-upload
          v-model:file-list="imageFileList"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :action="url"
          :on-success="handleImageFileSuccess"
          scoped-slot="fill"
        >
          <el-icon>
            <Plus/>
          </el-icon>
        </el-upload>
        <el-dialog v-model="dialogVisible" style="width: fit-content">
          <el-image style="height: 700px" :src="dialogImageUrl" fit="contain" alt="Preview Image"/>
        </el-dialog>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage, UploadProps, UploadUserFile} from "element-plus";
import { Plus } from '@element-plus/icons-vue'
import app from "@/constants/app";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const url = ref(app.data_api + "/api/v1/upload");
const uploading = ref(false);

declare type ImageInfo = {
  name: string
  url: string | undefined
}
declare type DataForm = {
  id: string,
  workClothesName: string,
  workClothesType: string,
  remark: string,
  created: string,
  modified: string,
  deleted: string,
  imageInfo: ImageInfo[] | string,
}


const dataForm = reactive({
  id: '',
  workClothesName: '',
  workClothesType: '',
  remark: '',
  created: '',
  modified: '',
  deleted: '',
  imageInfo: <ImageInfo[]>[],
} as DataForm);

const imageFileList = ref<UploadUserFile[]>([])

const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}

const handleImageFileSuccess = (data: any) => {
  if (data && data.code === 0) {
    //更新图像地址从本地游览器缓存到文件储存服务地址
    imageFileList.value[imageFileList.value.length - 1].url = app.data_api + data.data.url
    uploading.value = false;
    ElMessage({
      type: "success",
      message: "工服图片上传成功!"
    });
  } else {
    ElMessage({
      type: "error",
      message: "工服图片上传失败!"
    });
  }
};

const rules = ref({
  workClothesName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  workClothesType: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  // 重置图片上传缓存
  if (imageFileList.value.length > 0) {
    imageFileList.value = []
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/workclothesinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    let imageInfo = JSON.parse(dataForm.imageInfo as string)
    if (Array.isArray(imageInfo)) {
      imageFileList.value = imageInfo
    } else {
      imageFileList.value = []
    }
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    let imageInfoList = <ImageInfo[]>[]
    for (const imageFile of imageFileList.value) {
      imageInfoList.push({
        name: imageFile.name,
        url: imageFile.url
      })
    }
    dataForm.imageInfo = JSON.stringify(imageInfoList);

    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/workclothesinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
