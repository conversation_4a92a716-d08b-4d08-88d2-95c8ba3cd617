<!-- Flowchart.vue -->
<script setup>
import {useVueFlow, VueFlow} from '@vue-flow/core'
import {Background} from '@vue-flow/background'
import {ref} from 'vue'

const props = defineProps({
  onStageSelect: Function // 声明父组件传递的方法
});

const {
  onNodeClick,
} = useVueFlow()

const elements = ref([
  {
    id: 'baseInfo',
    label: '训练基础信息',
    position: {x: 250, y: 30},
  },
  {
    id: 'baseConfig',
    label: '基础模型配置',
    position: {x: 270, y: 110},
  },
  {
    id: 'dataSet',
    label: '数据集配置',
    position: {x: 250, y: 190},
  },
  {
    id: 'trainConfig',
    label: '训练配置',
    position: {x: 270, y: 270},
  },
  {
    id: 'exportConfig',
    label: '导出配置',
    position: {x: 250, y: 350},
  },
  {
    id: 'e1-2',
    source: 'baseInfo',
    target: 'baseConfig',
  },
  {
    id: 'e2-3',
    source: 'baseConfig',
    target: 'dataSet',
  },
  {
    id: 'e3-4',
    source: 'dataSet',
    target: 'trainConfig',
  },
  {
    id: 'e4-5',
    source: 'trainConfig',
    target: 'exportConfig',
  },
])

onNodeClick((event) => {
  props.onStageSelect(event.node.id)
})

</script>

<template>
  <VueFlow v-model="elements" style="width: 100%; height: 460px">
    <Background pattern-color="#aaa" :gap="16"/>
  </VueFlow>
</template>
