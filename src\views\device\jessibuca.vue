<template>
  <div ref="container" @dblclick="fullscreenSwitch"
       style="width:100%; height: 100%; background-color: #000000;margin:0 auto;position: relative;">
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';

declare global {
  interface Window {
    Jessibuca: any;
  }
}

export default defineComponent({
  name: 'Jessibuca',
  props: {
    videoUrl: {
      type: String,
      required: true,
    },
    hasAudio: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const container = ref<HTMLDivElement | null>(null);
    const playing = ref(false);
    const isNotMute = ref(false);
    const fullscreen = ref(false);
    const loaded = ref(false);
    const kBps = ref(0);
    const btnDom = ref<HTMLDivElement | null>(null);

    const route = useRoute();

    let jessibucaPlayer: any = null;

    watch(
      () => props.videoUrl,
      (newVal) => {
        nextTick(() => {
          play(newVal);
        });
      },
      { immediate: true }
    );


    function create() {
      const options = {
        container: container.value,
        autoWasm: true,
        background: '',
        controlAutoHide: true,
        debug: false,
        decoder: './decoder.js',
        forceNoOffscreen: true,
        hasAudio: props.hasAudio,
        heartTimeout: 30,
        heartTimeoutReplay: true,
        heartTimeoutReplayTimes: 3,
        hiddenAutoPause: false,
        hotKey: true,
        isFlv: false,
        isFullResize: false,
        isNotMute: isNotMute.value,
        isResize: true,
        keepScreenOn: true,
        loadingText: '请稍等, 视频加载中......',
        loadingTimeout: 30,
        loadingTimeoutReplay: true,
        loadingTimeoutReplayTimes: 2,
        openWebglAlignment: false,
        operateBtns: {
          fullscreen: true,
          screenshot: true,
          play: true,
          audio: true,
          record: true,
        },
        recordType: 'mp4',
        rotate: 0,
        showBandwidth: true,
        supportDblclickFullscreen: false,
        timeout: 30,
        useMSE: true,
        useWCS: true,
        useWebFullScreen: true,
        videoBuffer: 0.1,
        wasmDecodeErrorReplay: true,
        wcsUseVideoRender: false,
      };
      if (typeof window.Jessibuca !== 'function') {
        console.error('Jessibuca 未正确加载');
        return;
      }

      jessibucaPlayer = new window.Jessibuca(options);

      jessibucaPlayer.on('pause', () => {
        playing.value = false;
      });
      jessibucaPlayer.on('play', () => {
        playing.value = true;
      });
    }

    function play(url: string) {
      console.log('Jessibuca -> url: ', url);
      if (jessibucaPlayer) {
        destroy();
      }
      create();
      jessibucaPlayer.on('play', () => {
        playing.value = true;
        loaded.value = true;
      });
      if (jessibucaPlayer.hasLoaded()) {
        jessibucaPlayer.play(url);
      } else {
        jessibucaPlayer.on('load', () => {
          jessibucaPlayer.play(url);
        });
      }
    }

    function pause() {
      if (jessibucaPlayer) {jessibucaPlayer.pause();
      }
      playing.value = false;
    }

    function screenshot() {
      if (jessibucaPlayer) {
        jessibucaPlayer.screenshot();
      }
    }

    function mute() {
      if (jessibucaPlayer) {
        jessibucaPlayer.mute();
      }
    }

    function cancelMute() {
      if (jessibucaPlayer) {
        jessibucaPlayer.cancelMute();
      }
    }

    function destroy() {
      if (jessibucaPlayer) {
        jessibucaPlayer.destroy();
      }
      if (btnDom.value && !document.getElementById('buttonsBox')) {
        container.value?.appendChild(btnDom.value);
      }
      jessibucaPlayer = null;
      playing.value = false;
    }

    function fullscreenSwitch() {
      const isFull = isFullscreen();
      jessibucaPlayer.setFullscreen(!isFull);
      fullscreen.value = !isFull;
    }

    function isFullscreen() {
      return (
        document.fullscreenElement ||
        (document as any).msFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).webkitFullscreenElement ||
        false
      );
    }

    function playBtnClick() {
      play(props.videoUrl);
    }


    return {
      container,
      playing,
      isNotMute,
      fullscreen,
      loaded,
      kBps,
      btnDom,
      create,
      play,
      pause,
      screenshot,
      mute,
      cancelMute,
      destroy,
      fullscreenSwitch,
      isFullscreen,
      playBtnClick,
    };
  },
});
</script>

<style>
.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;
}

.jessibuca-btn {
  width: 20px;
  color: rgb(255, 255, 255);
  line-height: 27px;
  margin: 0px 10px;
  padding: 0px 2px;
  cursor: pointer;
  text-align: center;
  font-size: 0.8rem !important;
}

.buttons-box-right {
  position: absolute;
  right: 0;
}
</style>
