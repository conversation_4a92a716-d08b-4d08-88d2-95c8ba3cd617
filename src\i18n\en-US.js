const t = {}

t.loading = 'Loading...'

t.brand = {}
t.brand.lg = 'Edge Computed Admin'
t.brand.mini = 'UCAS'

t.add = 'Add'
t.delete = 'Delete'
t.download = 'Download'
t.deleteBatch = 'Delete'
t.update = 'Update'
t.play = 'Play'
t.query = 'Query'
t.export = 'Export'
t.handle = 'Operation'
t.confirm = 'Confirm'
t.cancel = 'Cancel'
t.clear = 'Clear'
t.logout = 'Logout'
t.manage = 'Manage'
t.createDate = 'Create Date'
t.keyword = 'Keyword:'
t.choose = 'Choose'
t.start = 'Start'
t.stop = 'Stop'
t.restart = 'Restart'
t.status = 'Status'

t.prompt = {}
t.prompt.title = 'Prompt'
t.prompt.info = 'Are you sure you want to perform this [{handle}] operation?'
t.prompt.success = 'Operation successful'
t.prompt.failed = 'Operation failed'
t.prompt.deleteBatch = 'Please select items to delete'

t.validate = {}
t.validate.required = 'Required fields cannot be empty'
t.validate.format = 'Incorrect format for {attr}'

t.upload = {}
t.upload.text = 'Drag the file here, or <em>click to upload</em>'
t.upload.tip = 'Only {format} format files are supported!'
t.upload.button = 'Click to upload'

t.datePicker = {}
t.datePicker.range = 'to'
t.datePicker.start = 'Start Date'
t.datePicker.end = 'End Date'

t.fullscreen = {}
t.fullscreen.prompt = 'Your browser does not support this operation'

t.updatePassword = {}
t.updatePassword.title = 'Change Password'
t.updatePassword.loginOut = 'Login Out'
t.updatePassword.username = 'Username'
t.updatePassword.password = 'Current Password'
t.updatePassword.newPassword = 'New Password'
t.updatePassword.confirmPassword = 'Confirm Password'
t.updatePassword.validate = {}
t.updatePassword.validate.confirmPassword = 'The confirm password does not match the new password input'

t.contentTabs = {}
t.contentTabs.closeCurrent = 'Close Current Tab'
t.contentTabs.closeOther = 'Close Other Tabs'
t.contentTabs.closeAll = 'Close All Tabs'
t.contentTabs.more = 'More'
t.contentTabs.hide = 'Hide'

/* Page */
t.notFound = {}
t.notFound.desc = 'Sorry, the page you are looking for has <em>disappeared</em>...'
t.notFound.back = 'Back'
t.notFound.home = 'Home'

t.login = {}
t.login.title = 'Login'
t.login.username = 'Username'
t.login.password = 'Password'
t.login.captcha = 'Captcha'
t.login.demo = 'Demo'
t.login.copyright = 'UCAS'

t.edge = {}
// home
t.edge.processor = "Processor";
t.edge.cores = 'Cores';
t.edge.memory = "Memory";
t.edge.storage = "Storage";
t.edge.temperature = "Temperature";
t.edge.runtime = "Runtime";

// Node Management
t.edge.sname = 'Node Name'
t.edge.nodeMacAddress = 'Device Node Network MAC Address'
t.edge.interfaceName = 'Interface Name'
t.edge.networkCardSpeed = 'Network Card Speed';
t.edge.ipv4Address = 'IPv4 Address';
t.edge.ipv4Gateway = 'IPv4 Gateway';
t.edge.ipv4Method = 'IPv4 Method';
t.edge.ipv4SubnetMask = 'IPv4 Subnet Mask';
// Device Management
t.edge.nodeID = 'Node ID';
t.edge.cache = 'Cache';
t.edge.cpuLoad1 = 'CPU Load 1';
t.edge.cpuLoad15 = 'CPU Load 15';
t.edge.cpuLoad5 = 'CPU Load 5';
t.edge.cpuCores = 'CPU Cores';
t.edge.freeMemory = 'Free Memory';
t.edge.sharedMemory = 'Shared Memory';
t.edge.temperature = 'Temperature';
t.edge.totalDisk = 'Total Disk';
t.edge.totalMemory = 'Total Memory';
t.edge.uptime = 'Uptime';
t.edge.usedDisk = 'Used Disk';
t.edge.computingPower = 'Computing Power';
// Network Management
t.edge.physicalAddress = "Physical Address"
t.edge.duplex = "Duplex"
t.edge.power = "Power"
t.edge.networkCardSpeed = "Network Card Speed"
t.edge.networkCardSpeedSupport = "Network Card Speed Support"
t.edge.creationTime = "Creation Time"
t.edge.updateTime = "Update Time"
t.edge.deletionTime = "Deletion Time"
t.edge.onOrOff = 'On/Off'
t.edge.chooseIPv4Method = "Choose IPv4 Method"
t.edge.selectActiveArea = "Select Active Area"
t.edge.autoGet = "Auto Get";
t.edge.manualConfig = "Manual Configuration";
t.edge.unknown = "Unknown";
// Camera Management
t.edge.name = "Name"
t.edge.cameraIPAddress = "Camera IP Address";
t.edge.type = "Type";
t.edge.cameraManufacturer = "Camera Manufacturer";
t.edge.videoStreamAddress1 = "Video Stream Address 1";
t.edge.cameraID = "Camera ID";
t.edge.username = "Username";
t.edge.password = "User Password";
t.edge.longitude = "Longitude";
t.edge.latitude = "Latitude";
// Model Management
t.edge.modelName = "Model Name";
t.edge.modelFileName = "Model File Name";
t.edge.modelSize = "Model Size";
t.edge.labelFileName = "Label File Name";
t.edge.selectModel = "Select Model";
t.edge.labelModel = "Label Model";
t.edge.datasetsName = "Datasets";
// Push Management
t.edge.pushName = "Push Name";
t.edge.pushURL = "Push URL";
// Link Management
t.edge.linkName = "Link Name";
t.edge.cameraName = "Camera Name";
t.edge.cameraUUID = "Camera UUID";
t.edge.allNodesLinkNodes = "All Nodes LinkNodes";
t.edge.model = 'Model';
t.edge.push = 'Push';
t.edge.pleaceAddNode = 'Please add a node'
t.edge.addNode = 'Add Node'
t.edge.fillModelAndPushInfo = "Please fill in model and push information:";
t.edge.targetTracking = "Target Tracking";
t.edge.frameByFrameDetection = "Frame-by-Frame Detection";
t.edge.roiRegion = "ROI Region";
t.edge.pushMultiple = "Push (Multiple)";
t.edge.selectPush = "Select Push";
t.edge.confirmAddNode = "Confirm Add Node";
// Link Deployment
t.edge.linkID = "Link ID";
t.edge.linkStatus = "Link Status";
t.edge.modelDeploymentPath = "Model Deployment Path";
t.edge.parameterConfiguration = "Parameter Configuration";
t.edge.parameterConfigurationInfo = "Parameter Configuration Info";
t.edge.parameterConfigurationConfirm = 'Pleace Enter The Correct Parameter Settings'
t.edge.remarks = "Remarks";
t.edge.showing = 'View';
t.edge.selectLink = "Select Link";
t.edge.linkStatusContent = 'Link Status: 1. Deploying; 2. Deployment Complete; 3. Running; 4. Node Stopped; 5. Node Lost; 99: Exception'
// Detection Data
t.edge.primaryKeyID = "Primary Key ID";
t.edge.serialNumber = "Serial Number";
t.edge.deviceID = "Device ID";
t.edge.originalImageURL = "Original Image URL";
t.edge.boundingBoxImageURL = "Bounding Box Image URL";
t.edge.keyword = "Keyword";
t.edge.videoID = "Video ID";
t.edge.fileFormat = "File Format";
t.edge.timeSort = "Time Sort";
t.edge.alertLevel = "Alert Level";
t.edge.contentDescription = "Content Description";
t.edge.deploymentLocation = "Deployment Location";
t.edge.deploymentLocationLongitude = "Deployment Location Longitude";
t.edge.cameraIndexCode = "Camera Index Code";
t.edge.detectionTime = "Detection Time";
t.edge.targetListData = "Target List Data";
t.edge.behaviorDescription = "0: No Exception in Target Detection, 1: Aggregation, 3: Card Clash, 4: Carrying Weapons, 5: Fighting, 7: Illegal Parking, 8: Wrong-Way, 13: Falling"
t.edge.anomalyBehavior = "Anomalous Behavior Description, Anomalous Behavior Marking"
t.edge.targetListDataContent = "Category: car, person, bus, etc., Target Coordinates, No Coordinates in Anomalous Behavior Detection, Directly Transmit the Original Frame"
t.edge.time = "Time";
t.edge.securityLevelContent = "5: Pedestrian and Vehicle Detection, Small Object Detection, 4: Fallen, Illegal Parking, etc., 3: Fighting, Armed, etc.";

t.schedule = {}
t.schedule.beanName = 'Bean Name'
t.schedule.beanNameTips = 'Spring bean name, e.g., testTask'
t.schedule.pauseBatch = 'Pause'
t.schedule.resumeBatch = 'Resume'
t.schedule.runBatch = 'Execute'
t.schedule.log = 'Log List'
t.schedule.params = 'Parameters'
t.schedule.cronExpression = 'Cron Expression'
t.schedule.cronExpressionTips = 'E.g., 0 0 12 * * ?'
t.schedule.remark = 'Remarks'
t.schedule.status = 'Status'
t.schedule.status0 = 'Paused'
t.schedule.status1 = 'Normal'
t.schedule.statusLog0 = 'Failed'
t.schedule.statusLog1 = 'Success'
t.schedule.pause = 'Pause'
t.schedule.resume = 'Resume'
t.schedule.run = 'Execute'
t.schedule.jobId = 'Job ID'
t.schedule.times = 'Time Consumed (Milliseconds)'
t.schedule.createDate = 'Execution Time'

t.oss = {}
t.oss.config = 'Cloud Storage Configuration'
t.oss.upload = 'Upload File'
t.oss.url = 'URL Address'
t.oss.createDate = 'Create Date'
t.oss.type = 'Type'
t.oss.type1 = 'Qiniu'
t.oss.type2 = 'Aliyun'
t.oss.type3 = 'Tencent Cloud'
t.oss.qiniuDomain = 'Domain'
t.oss.qiniuDomainTips = 'Qiniu bound domain'
t.oss.qiniuPrefix = 'Path Prefix'
t.oss.qiniuPrefixTips = 'Default is empty if not set'
t.oss.qiniuAccessKey = 'AccessKey'
t.oss.qiniuAccessKeyTips = 'Qiniu AccessKey'
t.oss.qiniuSecretKey = 'SecretKey'
t.oss.qiniuSecretKeyTips = 'Qiniu SecretKey'
t.oss.qiniuBucketName = 'Bucket Name'
t.oss.qiniuBucketNameTips = 'Qiniu storage space name'
t.oss.aliyunDomain = 'Domain'
t.oss.aliyunDomainTips = 'Aliyun bound domain'
t.oss.aliyunPrefix = 'Path Prefix'
t.oss.aliyunPrefixTips = 'Default is empty if not set'
t.oss.aliyunEndPoint = 'EndPoint'
t.oss.aliyunEndPointTips = 'Aliyun EndPoint'
t.oss.aliyunAccessKeyId = 'AccessKeyId'
t.oss.aliyunAccessKeyIdTips = 'Aliyun AccessKeyId'
t.oss.aliyunAccessKeySecret = 'AccessKeySecret'
t.oss.aliyunAccessKeySecretTips = 'Aliyun AccessKeySecret'
t.oss.aliyunBucketName = 'Bucket Name'
t.oss.aliyunBucketNameTips = 'Aliyun BucketName'
t.oss.qcloudDomain = 'Domain'
t.oss.qcloudDomainTips = 'Tencent Cloud bound domain'
t.oss.qcloudPrefix = 'Path Prefix'
t.oss.qcloudPrefixTips = 'Default is empty if not set'
t.oss.qcloudAppId = 'AppId'
t.oss.qcloudAppIdTips = 'Tencent Cloud AppId'
t.oss.qcloudSecretId = 'SecretId'
t.oss.qcloudSecretIdTips = 'Tencent Cloud SecretId'
t.oss.qcloudSecretKey = 'SecretKey'
t.oss.qcloudSecretKeyTips = 'Tencent Cloud SecretKey'
t.oss.qcloudBucketName = 'Bucket Name'
t.oss.qcloudBucketNameTips = 'Tencent Cloud Bucket Name'
t.oss.qcloudRegion = 'Region'
t.oss.qcloudRegionTips = 'Select'
t.oss.qcloudRegionBeijing1 = 'Beijing Zone 1 (North China)'
t.oss.qcloudRegionBeijing = 'Beijing'
t.oss.qcloudRegionShanghai = 'Shanghai (East China)'
t.oss.qcloudRegionGuangzhou = 'Guangzhou (South China)'
t.oss.qcloudRegionChengdu = 'Chengdu (Southwest)'
t.oss.qcloudRegionChongqing = 'Chongqing'
t.oss.qcloudRegionSingapore = 'Singapore'
t.oss.qcloudRegionHongkong = 'Hong Kong'
t.oss.qcloudRegionToronto = 'Toronto'
t.oss.qcloudRegionFrankfurt = 'Frankfurt'

t.dept = {}
t.dept.name = 'Name'
t.dept.parentName = 'Parent Department'
t.dept.sort = 'Sort'
t.dept.parentNameDefault = 'Top-level Department'
t.dept.chooseerror = 'Please select a department'
t.dept.title = 'Select Department'

t.dict = {}
t.dict.dictName = 'Dictionary Name'
t.dict.dictType = 'Dictionary Type'
t.dict.dictLabel = 'Dictionary Label'
t.dict.dictValue = 'Dictionary Value'
t.dict.sort = 'Sort'
t.dict.remark = 'Remarks'
t.dict.createDate = 'Create Date'

t.logError = {}
t.logError.requestUri = 'Request URI'
t.logError.requestMethod = 'Request Method'
t.logError.requestParams = 'Request Parameters'
t.logError.ip = 'Operation IP'
t.logError.userAgent = 'User Agent'
t.logError.createDate = 'Create Date'
t.logError.errorInfo = 'Exception Information'

t.logLogin = {}
t.logLogin.creatorName = 'Username'
t.logLogin.status = 'Status'
t.logLogin.status0 = 'Failed'
t.logLogin.status1 = 'Success'
t.logLogin.status2 = 'Account Locked'
t.logLogin.operation = 'Operation Type'
t.logLogin.operation0 = 'Login'
t.logLogin.operation1 = 'Logout'
t.logLogin.ip = 'Operation IP'
t.logLogin.userAgent = 'User-Agent'
t.logLogin.createDate = 'Create Date'

t.logOperation = {}
t.logOperation.status = 'Status'
t.logOperation.status0 = 'Failed'
t.logOperation.status1 = 'Success'
t.logOperation.creatorName = 'Username'
t.logOperation.operation = 'User Operation'
t.logOperation.requestUri = 'Request URI'
t.logOperation.requestMethod = 'Request Method'
t.logOperation.requestParams = 'Request Parameters'
t.logOperation.requestTime = 'Request Duration'
t.logOperation.ip = 'Operation IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = 'Create Date'

t.menu = {}
t.menu.name = 'Name'
t.menu.icon = 'Icon'
t.menu.type = 'Type'
t.menu.type0 = 'Menu'
t.menu.type1 = 'Button'
t.menu.sort = 'Sort'
t.menu.url = 'Route'
t.menu.permissions = 'Authorization Identifier'
t.menu.permissionsTips = 'Separate multiple with commas, e.g., sys:menu:save,sys:menu:update'
t.menu.parentName = 'Parent Menu'
t.menu.parentNameDefault = 'Top-level Menu'
t.menu.resource = 'Authorization Resource'
t.menu.resourceUrl = 'Resource URL'
t.menu.resourceMethod = 'Request Method'
t.menu.resourceAddItem = 'Add an item'
t.menu.home = 'Home'
t.menu.algorithm = 'Algorithm'
t.menu.model = 'Deployed Scene'
t.menu.runningModel = 'Running Scene'
t.menu.stream = 'Video Stream'
t.menu.alarm = 'Today Alarm'
t.menu.monitor = 'System Monitor'
t.menu.cronjob = 'Cron Job'
t.menu.website = 'Website'
t.menu.database = 'Database'

t.params = {}
t.params.paramCode = 'Code'
t.params.paramValue = 'Value'
t.params.remark = 'Remarks'

t.role = {}
t.role.name = 'Name'
t.role.remark = 'Remarks'
t.role.createDate = 'Create Date'
t.role.menuList = 'Menu Authorization'
t.role.deptList = 'Data Authorization'

t.user = {}
t.user.username = 'Username'
t.user.deptName = 'Department'
t.user.email = 'Email'
t.user.mobile = 'Mobile Number'
t.user.status = 'Status'
t.user.status0 = 'Disabled'
t.user.status1 = 'Normal'
t.user.createDate = 'Create Date'
t.user.password = 'Password'
t.user.confirmPassword = 'Confirm Password'
t.user.realName = 'Full Name'
t.user.gender = 'Gender'
t.user.gender0 = 'Male'
t.user.gender1 = 'Female'
t.user.gender2 = 'Unknown'
t.user.roleIdList = 'Role Configuration'
t.user.validate = {}
t.user.validate.confirmPassword = 'The confirm password does not match the password input'
t.user.select = 'Select User'
t.user.selecterror = 'Please select one record'

t.home = {
  restart_1panel: 'Restart panel',
  restart_system: 'Restart server',
  operationSuccess: 'Operation succeeded, rebooting, please refresh the browser manually later!',
  overview: 'Overview',
  entranceHelper: `Security entrance isn't enabled. You can enable it in "Settings -> Security" to improve system security.`,
  appInstalled: 'Applications',
  systemInfo: 'System information',
  hostname: 'Hostname',
  platformVersion: 'Operating system',
  kernelVersion: 'Kernel',
  kernelArch: 'Architecture',
  network: 'Network',
  io: 'Disk I/O',
  ip: 'Local IP',
  proxy: 'System proxy',
  baseInfo: 'Base info',
  totalSend: 'Total sent',
  totalRecv: 'Total received',
  rwPerSecond: 'I/O operations',
  ioDelay: 'I/O latency',
  uptime: 'Up since',
  runningTime: 'Uptime',
  mem: 'System',
  swapMem: 'Swap partition',

  runSmoothly: 'Low load',
  runNormal: 'Moderate load',
  runSlowly: 'High load',
  runJam: 'Heavy load',

  core: 'Physical core',
  logicCore: 'Logical core',
  loadAverage: 'Load average in the last 1 minute | Load average in the last {n} minutes',
  load: 'Load',
  mount: 'Mount point',
  fileSystem: 'File system',
  total: 'Total',
  used: 'Used',
  free: 'Free',
  percent: 'Utilization',
  app: 'Recommended applications',
  goInstall: 'Go install',

  networkCard: 'Network card',
  disk: 'Disk',
  physicalCores: 'Physical Cores',
  logicalCores: 'Logical Cores',
  loadAvg: 'Load',
  loadAverage: 'Load Average ({count} min)',
  mount: 'Mount Point',
  fileSystem: 'File System',
  baseInfo: 'Base Info',
  mem: 'Memory',
  total: 'Total',
  used: 'Used',
  free: 'Free',
  percent: 'Usage',
  swapMem: 'Swap Memory',
  runSmoothly: 'Running Smoothly',
  runNormal: 'Running Normally',
  runSlowly: 'Running Slowly',
  runJam: 'Running Jammed'
}

// Add commons related translations
t.commons = {
    table: {
        status: 'Status Monitor',
        type: 'Type',
        all: 'All'
    },
    button: {
        showAll: 'Show All',
        hideSome: 'Hide Some'
    },
    units: {
        core: '{count} Core | {count} Cores',
        dayUnit: 'd',
        hourUnit: 'h',
        minuteUnit: 'm',
        secondUnit: 's'
        // time: 'time' // This might have been from the old structure, check if needed
    }
};

// Add monitor related translations
t.monitor = {
    up: 'Upload',
    down: 'Download',
    read: 'Read',
    write: 'Write',
    path: 'Path',
    type: 'Type',
    total: 'Total',
    used: 'Used',
    free: 'Free',
    usedPercent: 'Usage',
    index: 'Index',
    productName: 'Product Name',
    gpuUtil: 'GPU Utilization',
    temperature: 'Temperature',
    performanceState: 'Performance State',
    powerUsage: 'Power Usage',
    memoryUsage: 'Memory Usage',
    fanSpeed: 'Fan Speed',
    memory: 'Memory',
    disk: 'Disk'
}

// Add tabs translations
t.tabs = {
    more: 'More',
    hide: 'Hide'
};

export default t

