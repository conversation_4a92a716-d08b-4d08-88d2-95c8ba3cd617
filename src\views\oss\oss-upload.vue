<template>
  <el-dialog v-model="visible" title="上传文件" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-upload :action="url" :file-list="fileList" drag multiple :before-upload="beforeUploadHandle"
               :on-success="successHandle" class="text-center">
      <el-icon class="el-icon--upload">
        <upload-filled/>
      </el-icon>
      <div class="el-upload__text">将文件拖到此处，或点击上传</div>
      <template v-slot:tip>
      </template>
    </el-upload>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref} from "vue";
import {IObject} from "@/types/interface";
import app from "@/constants/app";
import {ElMessage} from "element-plus";
import baseService from "@/service/baseService";
import {OssMetaData} from "@/types/interface";
import axios from "axios";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const url = ref("");
const metaDataUrl = ref("");
const num = ref(0);
const fileList = ref<IObject[]>();
const props = defineProps(['fileType'])

const init = () => {
  visible.value = true;
  url.value = `${app.ossApi}/api/v1/minio-upload`;
  metaDataUrl.value = `${app.api}/oss/meta-data`;
  num.value = 0;
  fileList.value = [];
};

// 上传之前
const beforeUploadHandle = (file: IObject) => {
  num.value++;
};

// 上传成功
const successHandle = (res: IObject, file: IObject, list: IObject[]) => {
  if (res.code !== 0) {
    return ElMessage.error(res.msg);
  }
  fileList.value = list;
  num.value--;
  if (num.value === 0) {
    ElMessage.success({
      message: "成功",
      duration: 500,
      onClose: () => {
        visible.value = false;
        emit("refreshDataList");
      }
    });
  }
  // 保存文件元数据到业务后端
  const metaData:OssMetaData = {
    fileId: res.data.fileId,
    url: res.data.url,
    fileName: res.data.fileName,
    fileSize: 0,
    fileType: props.fileType,
    bucketName: "",
    objectName: "",
    prefix: ""
  }
  baseService.post(metaDataUrl.value,metaData)

  // 调用 label studio api 从oss同步。 目前暂时写死，后续绑定到业务系统时可能移动至后端实现
  const headers = {
    authorization: "Token 36f2bea6ee44c7399d30d2c32414a66d39e2a1db"
    // authorization: "Token 93a402fad24e8ad7e5a32a541c476cc7ff63cf30"
  }
  const syncUrl = "http://************:32656/api/storages/s3/1/sync"
  // const syncUrl = "http://***********:8080/api/storages/s3/1/sync"
  axios.post(syncUrl, undefined, { headers })
    .then(response => {
      console.log('Label-Studio 同步成功', response.data);
    })
    .catch(error => {
      console.error('Label-Studio 同步失败', error);
    });
};

defineExpose({
  init
});
</script>
