<template>
  <el-dialog align-center v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')"
             :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef"
             @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.linkName')" prop="linkName">
        <el-input v-model="dataForm.linkName" :placeholder="$t('edge.linkName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.cameraName')" prop="camera">
        <el-select class="m-2" v-model="dataForm.camera" value-key="id" :placeholder="$t('edge.selectModel')"
                   @change="onSelectCamera">
          <el-option v-for="item in cameraInfo" :key="item.id" :label="item.name" :value="item.uuid"/>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('edge.cameraUUID')" prop="cameraUuid">
        <el-input v-model="dataForm.cameraUuid" :placeholder="$t('edge.cameraUUID')"></el-input>
      </el-form-item>
      <el-form-item label="models" prop="nodes">
        <el-tree style="width: 100%" :data="nodeData" :default-checked-keys="defaultCheckedKeys" show-checkbox
                 node-key="id" default-expand-all :expand-on-click-node="false">
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span>
                <el-button type="danger" :icon="Delete" circle @click="removeNode(node, data)"/>
              </span>
            </span>
          </template>
        </el-tree>
        <!-- 恢复 -->
        <el-button color="rgba(50,122,230,1)" @click="visibleNestedDialog = true">{{ $t("edge.addNode") }}</el-button>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
  <el-dialog align-center v-model="visibleNestedDialog" @open="handleNestedDialog"
             :title="$t('edge.fillModelAndPushInfo')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="pushOrModelForm" destroy-on-close ref="pushOrModelRef"
             @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item size="large" :label="$t('edge.modelName')">
        <el-select size="large" clearable class="m-2" v-model="pushOrModelForm.modelSelectvalue" value-key="id"
                   :placeholder="$t('edge.selectModel')">
          <el-option v-for="item in modelInfo" :key="item.id" :label="item.smodelName" :value="item.smodelName"/>
        </el-select>
      </el-form-item>
      <el-form-item size="large" :label="$t('edge.targetTracking')">
        <el-switch size="large" v-model="pushOrModelForm.TrackTag" :active-value="1" :inactive-value="0"/>
      </el-form-item>
      <el-form-item size="large" :label="$t('edge.frameByFrameDetection')">
        <el-input-number size="large" v-model="pushOrModelForm.SkipTag" placeholder="skip number"/>
      </el-form-item>
      <el-form-item size="large" :label="$t('edge.roiRegion')">
        <el-switch size="large" v-model="pushOrModelForm.RoiTag" :active-value="1" :inactive-value="0"/>
      </el-form-item>
      <el-form-item size="large" label="ROIx">
        <el-input-number size="large" v-model="pushOrModelForm.RoiX" placeholder="image x"/>
      </el-form-item>
      <el-form-item size="large" label="ROIy">
        <el-input-number size="large" v-model="pushOrModelForm.RoiY" placeholder="image y"/>
      </el-form-item>
      <el-form-item size="large" label="ROIw">
        <el-input-number size="large" v-model="pushOrModelForm.RoiW" placeholder="image width"/>
      </el-form-item>
      <el-form-item size="large" label="ROIh">
        <el-input-number size="large" v-model="pushOrModelForm.RoiH" placeholder="image height"/>
      </el-form-item>
      <el-form-item :label="$t('edge.pushMultiple')">
        <el-select v-model="pushOrModelForm.initPushInfo" class="m-2" value-key="id"
                   :placeholder="$t('edge.selectPush')" size="large" multiple>
          <el-option v-for="item in pushInfo" :key="item.id" :label="item.sname" :value="item"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visibleNestedDialog = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="confirmAddNodeHandle">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {Delete} from "@element-plus/icons-vue";
import {ElMessage, ElNotification} from "element-plus";
import {globalLanguage} from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
import {Camerainfo, Modelinfo, Nodes, Tree} from "@/types/interface";

const {$t} = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const visibleNestedDialog = ref(false);
const dataFormRef = ref();
const defaultCheckedKeys = ref<Array<string | number>>([]);
const dataForm = reactive({
  id: "",
  linkName: "",
  camera: "",
  cameraUuid: "",
  nodes: [] as Nodes[]
});
const pushOrModelRef = ref();
const pushOrModelForm = reactive({
  modelSelectvalue: "",
  TrackTag: 0,
  SkipTag: 0,
  RoiTag: 0,
  RoiX: 0,
  RoiY: 0,
  RoiW: 1,
  RoiH: 1,
  initPushInfo: ""
});
const modelNodes = ref<Modelinfo[]>([]); // 模型节点
const initPushOrModel = initDataForm(pushOrModelForm);
const nodeData = ref<Tree[]>([]);
const cameraInfo = ref<Camerainfo[]>([]); // 摄像头列表
const pushInfo = ref<Modelinfo[]>([]); // 推送列表
const modelInfo = ref<Modelinfo[]>([]); // 模型列表
const initForm = initDataForm(dataForm);

const rules = ref({
  linkName: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  camera: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  cameraUuid: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  nodes: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});

const init = (id?: string) => {
  visible.value = true;
  dataForm.id = "";
  getAllInfo();
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    nodeData.value = [];
    Object.assign(dataForm, initForm);
  }

  if (id) {
    getInfo(id);
  }
};
const getNodeInfo = () => {
  let currentModel = modelInfo.value.filter((item) => pushOrModelForm.modelSelectvalue === item.smodelName);
  console.log(currentModel);

  return {
    id: currentModel[0].id,
    smodelName: pushOrModelForm.modelSelectvalue,
    smodelFileName: currentModel[0].smodelFileName,
    iinputSize: currentModel[0].iinputSize,
    slabelFile: currentModel[0].slabelFile,
    itrackTag: pushOrModelForm.TrackTag,
    iroiTag: pushOrModelForm.RoiTag,
    iskipTag: pushOrModelForm.SkipTag,
    sroiX: pushOrModelForm.RoiX,
    sroiY: pushOrModelForm.RoiY,
    sroiW: pushOrModelForm.RoiW,
    sroiH: pushOrModelForm.RoiH
  };
};
// 得到所有的节点信息
const getAllNodesInfo = () => {
  let date = new Date().getTime();
  return {
    date,
    model: {
      ...modelNodes.value[modelNodes.value.length - 1]
    },
    push: [...pushOrModelForm.initPushInfo],
    children: []
  };
};
// 确认添加节点
const confirmAddNodeHandle = () => {
  modelNodes.value.push({...getNodeInfo()});
  let newNodeData = modelNodes.value.map((item) => ({id: item.id, label: item.smodelName, children: []}));
  nodeData.value.push(...newNodeData);
  defaultCheckedKeys.value = [nodeData.value[0].id];
  dataForm.nodes.push({...getAllNodesInfo()});
  visibleNestedDialog.value = false;
  ElNotification({
    message: $t("prompt.success"),
    type: "success",
    position: "top-left"
  });
};
// 打开对话框
const handleNestedDialog = () => {
  if (pushOrModelRef.value) {
    pushOrModelRef.value.resetFields();
    Object.assign(pushOrModelForm, initPushOrModel);
  }
};
// 选择摄像头
const onSelectCamera = (value: any) => {
  let it = cameraInfo.value.find((item) => {
    return (item.uuid == value);
  });
  if (it?.name) {
    dataForm.cameraUuid = value;
    dataForm.camera = it.name;
  }
};
// 删除节点
const removeNode = (node: any, data: any) => {
  const parent = node.parent;
  const children: Tree[] = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  nodeData.value = [...nodeData.value];
  modelNodes.value = modelNodes.value.filter((item: any) => item.id !== data.id);
  dataForm.nodes = dataForm.nodes.filter((item: any) => item.model.id !== data.id);
};
// 获取信息
const getInfo = (id: string) => {
  baseService.get("/edge/tlinkinfo/" + id).then(({data}: any) => {
    Object.assign(dataForm, data);
    if (id) {
      nodeData.value = data.nodes.map((item: any) => {
        return {
          id: item.model.id,
          label: item.model.smodelName,
          children: []
        };
      });
      defaultCheckedKeys.value = data.nodes.map((item: any) => item.model.id);
    }
  });
};
// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/tcamerainfo/page"), //加载摄像头列表
    baseService.get("/edge/tmodelinfo/page"), //加载模型列表
    baseService.get("/edge/tpushinfo/page") //加载推送列表
  ]).then(([tcamerainfo, tmodelinfo, tpushinfo]) => {
    pushInfo.value = tpushinfo.data.list;
    modelInfo.value = tmodelinfo.data.list;
    cameraInfo.value = tcamerainfo.data.list;
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tlinkinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>

<style>
.el-tree-node__content {
  height: 50px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
