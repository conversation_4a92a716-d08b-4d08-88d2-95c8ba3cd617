<template>
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.id" placeholder="id" clearable style="width: 200px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()"></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)"
          @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{
          $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{
          $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="state.dataList"
      show-overflow-tooltip border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <!-- <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="sname" :label="$t('edge.pushName')" header-align="center" width="250" align="center"></el-table-column>
      <el-table-column prop="sprotoName" :label="$t('edge.pushURL')" header-align="center" align="center">
        <template #default="scope">
          <el-tooltip :content="scope.row.sprotoName" placement="top" :show-after="100">
            <span>{{ truncateText(scope.row.sprotoName, 15) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="susername" :label="$t('edge.username')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="spassword" :label="$t('edge.password')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="stoken" label="token" header-align="center" align="center"></el-table-column> -->
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template #default="scope">
          <el-button type="warning" v-if="state.hasPermission('sys:user:update')" link
            @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" link v-if="state.hasPermission('sys:user:delete')"
            @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit"
      :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle"
      @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./tpushinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { Delete, Edit, Search, Share, Upload } from '@element-plus/icons-vue'
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/tpushinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tpushinfo/export",
  deleteURL: "/edge/tpushinfo",
  deleteIsBatch: true,
  dataForm: {
    id: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

//隐藏文本
const truncateText = (text: string, maxLength: number) => { 
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
