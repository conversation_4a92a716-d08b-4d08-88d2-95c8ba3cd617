<template>
  <!-- 模型管理 -->
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.id" placeholder="id" clearable style="width:200px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()" ></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column prop="smodelName" :label="$t('edge.modelName')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="smodelFileName" :label="$t('edge.modelFileName')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="iinputSize" :label="$t('edge.modelSize')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="slabelFile" :label="$t('edge.labelFileName')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template #default="scope">
          <el-button type="warning" v-if="state.hasPermission('sys:user:update')" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" link v-if="state.hasPermission('sys:user:delete')" @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./tmodelinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { Delete, Edit, Search, Share, Upload } from '@element-plus/icons-vue'
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/tmodelinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tmodelinfo/export",
  deleteURL: "/edge/tmodelinfo",
  deleteIsBatch: true,
  dataForm: {
    id: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
