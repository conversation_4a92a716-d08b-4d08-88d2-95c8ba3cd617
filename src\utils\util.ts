/**
 * 格式化日期为 YYYY-MM-DD HH:MM:SS 格式
 * @param time
 * @returns
 */
export function dateFormatForSecond(time: number | Date): string {
  const date = time instanceof Date ? time : new Date(time);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 计算文件大小，自动选择单位
 * @param size 字节大小
 * @returns
 */
export function computeSize(size: number): string {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + ' MB';
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB';
  }
}

/**
 * 从KB转换并计算大小
 * @param sizeKB 以KB为单位的大小
 * @returns
 */
export function computeSizeFromKBs(sizeKB: number): string {
  return computeSize(sizeKB * 1024);
}
