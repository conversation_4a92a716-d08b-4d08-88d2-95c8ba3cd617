import { CacheToken } from "@/constants/cacheKey";
import { IObject } from "@/types/interface";
import router, { getSysRouteMap } from "@/router";
import baseService from "@/service/baseService";
import { removeCache } from "@/utils/cache";
import { mergeServerRoute } from "@/utils/router";
import { defineStore } from "pinia";
import app from "@/constants/app";
import { ref } from "vue";
import { ElNotification } from "element-plus";

const securityLevelTexts: any = {
  1: "异常火点",
  2: "异常烟雾",
  3: "违规闯入",
  4: "人员脱岗",
  5: "人员摔倒",
  6: "人员超限",
  7: "人员趴睡",
  8: "安全帽佩戴",
  9: "工服穿戴",
  10: "不按规定车道行驶",
  11: "道路垃圾识别",
  12: "车辆拥堵识别",
  13: "车牌识别",
  14: "机动车违停",
  15: "车辆压线",
  16: "车辆逆行",
  17: "人员徘徊",
  18: "偷倒渣土",
  19: "雨水池污水口排放识别",
  20: "车辆抛洒垃圾等杂物",
  21: "人员攀爬",
  22: "渣土车无顶盖、篷布识别",
  23: "车辆超载违规",
  24: "管廊管道有异物遮盖识别",
  25: "危化品车辆识别"
};
let isManualClose = false;
const wsUrl = ref(app.api);
const type = ref();
const queryData = ref(0); // 存储查询结果
const pollingInterval = ref(10000); // 轮询间隔时间（毫秒）
let audio = new Audio();
const sum = ref(0);

function manualCloseAll() {
  isManualClose = true; // 设置标志位
  ElNotification.closeAll();
}

const audioPlay = () => {
  //关闭上一个提示框
  manualCloseAll();
  sum.value++;
  ElNotification({
    title: `警告  ${sum.value}`,
    // message: securityLevelTexts[type.value],
    message: "发现异常",
    duration: 0,
    type: "warning",
    onClose: () => {
      if (!isManualClose) {
        // 如果不是手动关闭，才执行这里的逻辑
        audio == null ? null : audio.pause();
        sum.value = 0;
      }
      isManualClose = false; // 重置标志位
      // audio == null ? null : audio.pause()
      // sum.value = 0
    },
    onClick: () => {
      //点击跳转到异常预警页面
      router.push("/edge/detection");
    }
  });
  // 加载音频文件
  audio.src = "video/alert.mp3";

  // 当音频可以播放时的处理程序
  audio.oncanplay = () => {
    // 播放音频
    audio.play();
    audio.loop = true;
    setTimeout(() => {
      audio.pause();
    }, 1 * 60 * 1000);
  };

  // 当音频播放结束时的处理程序
  audio.onended = () => {
    console.log("音频播放结束");
  };

  // 当音频发生错误时的处理程序
  audio.onerror = (error) => {
    console.error("音频加载或播放出错", error);
  };

  // 当音频暂停时的处理程序
  audio.onpause = () => {
    console.log("音频暂停");
  };

  // 当音频播放时的处理程序
  audio.onplay = () => {};
};
const fetchData = async () => {
  // try {
  //   const hash = window.location.hash;
  //   // 仅在当前不是登录页时请求数据
  //   if (hash !== "#/login" && hash !== "/login") {
  //     const response = await baseService.get("/edge/detection/page");
  //     // 仅在数据更新时播放警告音频并更新数据
  //     if (queryData.value < response.data.total) {
  //       if (queryData.value !== 0) {
  //         // 播放警告音频
  //         audioPlay();
  //       }
  //     }
  //     queryData.value = response.data.total;
  //   }
  // } catch (error) {
  //   console.error("查询数据失败:", error);
  // }
};

const startPolling = () => {
  // 启动轮询
  setInterval(() => {
    const isPollingActive =
      localStorage.getItem("isPollingActive") === null
        ? true
        : localStorage.getItem("isPollingActive") === "true";
    if (isPollingActive) {
      fetchData();
      // console.log(1);
    }
  }, pollingInterval.value);
};
startPolling();
// setInterval(() => {
// audioPlay()

// },2000);

/*生产环境下传入的app.api为"，请求会直接访问到UI的Nginx，通过路径代理转发到后端API上，使用split分割URL会返回undefined，所以针对ws协议单独拼接*/
// if (wsUrl.value !== ""){
//   wsUrl.value = 'ws://'+wsUrl.value.split('//')[1]+'/webSocket'
// }else {
//   wsUrl.value = 'ws://'+window.location.host+'/webSocket'
// }
// console.log("Connecting wslUrl is :",wsUrl.value)
// const ws = new WebSocket(wsUrl.value)
//
// ws.onopen = () => {
//     console.log("websocket 连接成功");
//   };
// ws.onmessage = (message) => {
//   if(window.location.hash !== '#/login'){
//     if(message.data != null){
//       const types = JSON.parse(message.data)
//       type.value = types.type
//       audioPlay()
//         }
//   }
//
//   }
export const useAppStore = defineStore("useAppStore", {
  state: () => ({
    state: {
      appIsLogin: false, //是否登录
      appIsReady: false, //app数据是否就绪
      appIsRender: false, //app是否开始渲染内容
      permissions: [], //权限集合
      user: {
        createDate: "",
        deptId: "",
        deptName: "",
        email: "",
        gender: 0,
        headUrl: "",
        id: "",
        mobile: "",
        postIdList: "",
        realName: "",
        roleIdList: "",
        status: 0,
        superAdmin: 0,
        username: ""
      }, //用户信息
      dicts: [], //字典
      routes: [], //最终的路由集合
      menus: [], //菜单集合
      address: {}, //地址集合
      routeToMeta: {}, //url对应标题meta信息
      tabs: [], //tab标签页集合
      activeTabName: "", //tab当前焦点页
      closedTabs: [] //存储已经关闭过的tab
    } as IObject
  }),
  actions: {
    updateState(data: IObject) {
      Object.keys(data).forEach((x: string) => {
        this.state[x] = data[x];
      });
    },
    initApp() {
      return Promise.all([
        baseService.get("/sys/menu/nav"), //加载菜单
        baseService.get("/sys/menu/permissions"), //加载权限
        baseService.get("/sys/user/info"), //加载用户信息
        baseService.get("/sys/dict/type/all") //加载字典
      ]).then(([menus, permissions, user, dicts]) => {
        if (user.code !== 0) {
          console.error("初始化用户数据错误", user.msg);
        }
        const [routes, routeToMeta] = mergeServerRoute(menus.data || [], getSysRouteMap());
        this.updateState({
          permissions: permissions.data || [],
          user: user.data || {},
          dicts: dicts.data || [],
          routeToMeta: routeToMeta || {},
          menus: []
        });
        return routes;
      });
    },
    //退出
    logout() {
      removeCache(CacheToken, true);
      this.updateState({
        appIsLogin: false,
        permissions: [],
        user: {},
        dicts: [],
        menus: [],
        address: {},
        routes: [],
        tabs: [],
        activeTabName: ""
      });
    }
  }
});
