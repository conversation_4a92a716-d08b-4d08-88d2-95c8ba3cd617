<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item label="nodeId" prop="nodeId">
        <el-input v-model="dataForm.nodeId" placeholder="nodeId"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.nodeID')" prop="nodeId">
        <el-input v-model="dataForm.nodeId" :placeholder="$t('edge.nodeID')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.cache')" prop="bufferram">
        <el-input v-model="dataForm.bufferram" :placeholder="$t('edge.cache')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.cpuLoad1')" prop="cpuload1">
        <el-input v-model="dataForm.cpuload1" :placeholder="$t('edge.cpuLoad1')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.cpuLoad15')" prop="cpuload15">
        <el-input v-model="dataForm.cpuload15" :placeholder="$t('edge.cpuLoad15')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.cpuLoad5')" prop="cpuload5">
        <el-input v-model="dataForm.cpuload5" :placeholder="$t('edge.cpuLoad5')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.cpuCores')" prop="cpunums">
        <el-input v-model="dataForm.cpunums" :placeholder="$t('edge.cpuCores')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.freeMemory')" prop="freeram">
        <el-input v-model="dataForm.freeram" :placeholder="$t('edge.freeMemory')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.sharedMemory')" prop="sharedram">
        <el-input v-model="dataForm.sharedram" :placeholder="$t('edge.sharedMemory')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.temperature')" prop="temp">
        <el-input v-model="dataForm.temp" :placeholder="$t('edge.temperature')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.totalDisk')" prop="totaldisk">
        <el-input v-model="dataForm.totaldisk" :placeholder="$t('edge.totalDisk')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.totalMemory')" prop="totalram">
        <el-input v-model="dataForm.totalram" :placeholder="$t('edge.totalMemory')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.uptime')" prop="uptime">
        <el-input v-model="dataForm.uptime" :placeholder="$t('edge.uptime')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.usedDisk')" prop="usedisk">
        <el-input v-model="dataForm.usedisk" :placeholder="$t('edge.usedDisk')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.computingPower')" prop="tops">
        <el-input v-model="dataForm.tops" :placeholder="$t('edge.computingPower')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  nodeId: "",
  bufferram: "",
  cpuload1: "",
  cpuload15: "",
  cpuload5: "",
  cpunums: "",
  freeram: "",
  sharedram: "",
  temp: "",
  totaldisk: "",
  totalram: "",
  uptime: "",
  usedisk: "",
  tops: ""
});
const initForm = initDataForm(dataForm);
const rules = ref({
  nodeId: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  bufferram: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  cpuload1: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  cpuload15: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  cpuload5: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  cpunums: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  freeram: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sharedram: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  temp: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  totaldisk: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  totalram: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  uptime: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  usedisk: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/tsysteminfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tsysteminfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
