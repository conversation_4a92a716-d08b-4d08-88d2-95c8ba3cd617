<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="镜像名" prop="imgName">
        <el-input v-model="dataForm.imgName" placeholder="镜像名"></el-input>
      </el-form-item>
      <el-form-item label="镜像url" prop="imgUrl">
        <el-input v-model="dataForm.imgUrl" placeholder="镜像url"></el-input>
      </el-form-item>

      <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="类型" filterable @change="handleTypeChange">
          <el-option v-for="item in alarmTypeInfo" :key="item.securityLevel" :label="item.describeInfo"
                     :value="item.securityLevel"/>
        </el-select>
      </el-form-item>

      <el-form-item label="算法标签" prop="label">
        <el-input v-model="dataForm.label" placeholder="算法类型标签（MLLM）"></el-input>
      </el-form-item>

      <el-form-item label="平台" prop="platform">
        <el-select v-model="dataForm.platform" placeholder="算法平台" @change="handlePlatformChange">
          <el-option label="CV" value="CV"/>
          <el-option label="NLP" value="NLP"/>
          <el-option label="MLLM" value="MLLM"/>
          <el-option label="LLM" value="LLM"/>
        </el-select>
      </el-form-item>

      <template v-if="dataForm.platform === 'MLLM' || dataForm.platform === 'LLM'">
        <el-form-item label="OpenAI Base URL" prop="openai_base_url">
          <el-input v-model="dataForm.openai_base_url" placeholder="OpenAI Base URL（MLLM）"></el-input>
        </el-form-item>
        <el-form-item label="OpenAI API Key" prop="open_api_key">
          <el-input v-model="dataForm.open_api_key" placeholder="OpenAI API Key（MLLM）"></el-input>
        </el-form-item>
        <el-form-item label="模型名称" prop="model">
          <el-input v-model="dataForm.model" placeholder="模型名称"></el-input>
        </el-form-item>
        <el-form-item label="prompt提示词" prop="prompt">
          <el-input v-model="dataForm.prompt" placeholder="prompt 提示词（MLLM）"></el-input>
        </el-form-item>
      </template>

    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

export interface AlarmTypeInfo {
  id: string | number;
  securityLevel?: string;
  describeInfo?: string;
}

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',
  imgName: '',
  imgUrl: '',
  platform: '',
  type: '',
  label: '',
  openai_base_url: '',
  open_api_key: '',
  prompt: '',
  model: '',
  memo: '',
  tenantId: '',
  twfCreated: '',
  twfModified: '',
  twfDeleted: ''
});

const rules = ref({
  imgName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  imgUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  type: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }

  // 设置默认值
  if (!dataForm.platform) {
    dataForm.platform = 'CV';
  }
};
const handlePlatformChange = (value: string) => {
  if (value !== 'MLLM' && value !== 'LLM') {
    // 清空 MLLM 特有字段
    dataForm.openai_base_url = '';
    dataForm.open_api_key = '';
    dataForm.model = '';
    dataForm.prompt = '';
  }
};

const alarmTypeInfo = ref<AlarmTypeInfo[]>([]);
// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/imagesinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });

  baseService.get("/edge/tmodelcategory/page", {limit: 100000}).then((res) => {
    alarmTypeInfo.value = res.data.list || [];
  });
};

// 处理类型选择变化
const handleTypeChange = (value: string) => {
  const selectedItem = alarmTypeInfo.value.find((item) => item.securityLevel === value);
  if (selectedItem) {
    dataForm.label = selectedItem.describeInfo || '';
  }
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/imagesinfo", dataForm).then(() => {
      ElMessage.success({
        message: "成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});

defineOptions({name: 'ImagesinfoAddOrUpdate'});
</script>
