<template>
  <div class="rr-login">
    <div class="rr-login-wrap">
      <!-- <div class="rr-login-left hidden-sm-and-down">
        <p class="rr-login-left-title">边缘计算平台管理系统</p>
      </div> -->
      <h1>榆林视频分析管理平台</h1>
      <div class="rr-login-right">
        <h2>门户登录验证</h2>
        <div class="rr-login-right-main">
          <el-form class="elform" ref="formRef" label-width="80px" :status-icon="true" :model="login" :rules="rules"
                   @keyup.enter="onLogin">
            <el-form-item label-width="0" prop="bearerToken">
              <el-input placeholder="Bearer Token" v-model="login.bearerToken" prefix-icon="lock"
                        autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label-width="0">
              <el-button color="#1E50A5" size="small" :disabled="state.loading" @click="onLogin"
                         class="rr-login-right-main-btn"> 登录
              </el-button>
            </el-form-item>
            <el-form-item label-width="0">
              <el-button color="#1E50A5" size="small" :disabled="state.loading" @click="goBack"
                         class="rr-login-right-main-btn"> 返回
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="login-footer">
    </div>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref} from "vue";
import {CacheToken} from "@/constants/cacheKey";
import baseService from "@/service/baseService";
import {setCache} from "@/utils/cache";
import {ElMessage} from "element-plus";
import {useAppStore} from "@/store";
import {useRoute, useRouter} from "vue-router";

const store = useAppStore();
const router = useRouter();

const state = reactive({
  loading: false,
});

const login = reactive({bearerToken: ""});

const formRef = ref();

const rules = ref({
  bearerToken: [{required: true, message: "BearerToken不能为空", trigger: "blur"}]
});

const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/yulin-login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            setCache(CacheToken, res.data, true);
            ElMessage.success("登录成功");
            router.push("/");
          } else {
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
        });
    }
  });
};

const goBack = () => {
  router.back();
}
onMounted(() => {
  //清理数据
  store.logout();

  //获取 bearer token
  const route = useRoute();
  let Authorization = route.query.Authorization as string
  Authorization = Authorization.replace("Bearer", "");
  login.bearerToken = Authorization

  onLogin()
})

</script>

<style lang="less" scoped>
// @import url("@/assets/theme/base.less");
.rr-login {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/images/beijin-login.png');
  background-size: cover;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  @media only screen and (max-width: 992px) {

  }

  .rr-login-right {
    width: 512px;
    height: 380px;
    background-color: white;
    border-radius: 20px;
    overflow: hidden;
  }

  h1 {
    text-align: center;
    color: white;
    font-size: 48px;
  }

  h2 {
    font-size: 24px;
    text-align: center;
  }

  .elform {
    width: 400px;
    margin: 0 auto;
  }

  .el-button {
    width: 400px;
    height: 50px;
    font-size: 22px;
  }

  .el-input {
    width: 400px;
    height: 50px;
    border: 5px;
    font-size: 18px;
    --el-input-focus-border-color: #1E50A5;
  }

  .captcha {
    width: 180px;
    background-color: #D1EBFF;
  }

  .captcha-img {
    width: 180px;
    margin-left: 30px;
  }
}
</style>
