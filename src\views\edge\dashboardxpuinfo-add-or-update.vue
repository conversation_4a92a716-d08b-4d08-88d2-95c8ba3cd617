<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="" prop="snapshotId">
        <el-input v-model="dataForm.snapshotId" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="deviceId">
        <el-input v-model="dataForm.deviceId" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="deviceName">
        <el-input v-model="dataForm.deviceName" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memory">
        <el-input v-model="dataForm.memory" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="temperature">
        <el-input v-model="dataForm.temperature" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryUsed">
        <el-input v-model="dataForm.memoryUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="power">
        <el-input v-model="dataForm.power" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryUtil">
        <el-input v-model="dataForm.memoryUtil" placeholder=""></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  snapshotId: '',  deviceId: '',  deviceName: '',  memory: '',  temperature: '',  memoryUsed: '',  power: '',  memoryUtil: ''});

const rules = ref({
          snapshotId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          deviceId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          deviceName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memory: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          temperature: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          power: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryUtil: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/dashboardxpuinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/dashboardxpuinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
