<template>
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.id" placeholder="id" clearable style="width: 200px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.nodeId" :placeholder="$t('edge.nodeID')" clearable style="width: 200px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()"></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column prop="nodeId" :label="$t('edge.nodeID')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="bufferram" :label="$t('edge.cache')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuload1" :label="$t('edge.cpuLoad1')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuload15" :label="$t('edge.cpuLoad15')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuload5" :label="$t('edge.cpuLoad5')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpunums" :label="$t('edge.cpuCores')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="freeram" :label="$t('edge.freeMemory')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="sharedram" :label="$t('edge.sharedMemory')" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="temp" :label="$t('edge.temperature')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="totaldisk" :label="$t('edge.totalDisk')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="totalram" :label="$t('edge.totalMemory')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="uptime" :label="$t('edge.uptime')" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="usedisk" :label="$t('edge.usedDisk')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="tops" :label="$t('edge.computingPower')" header-align="center" align="center"></el-table-column>
      <el-table-column label="查看详情" header-align="center" align="center" width="150px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="details(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template #default="scope">
          <el-button type="warning" v-if="state.hasPermission('sys:user:update')" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" link v-if="state.hasPermission('sys:user:delete')" @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>

  <el-dialog v-model="dialogTableVisible" title="设备状态详情" width="850" style="height: 350px; overflow: auto">
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100" style="height: 100px; text-align: center" v-for="item in gridData" :key="item.id">
      <tr>
        <td style="color: #327AE6; font-weight: bold">ID</td>
        <td>{{ item.id }}</td>
        <td style="color: #327AE6; font-weight: bold">节点ID</td>
        <td>{{ item.nodeId }}</td>
        <td style="color: #327AE6; font-weight: bold">缓存</td>
        <td>{{ item.bufferram }}</td>
      </tr>
      <tr>
        
        <td style="color: #327AE6; font-weight: bold">CPU1</td>
        <td>{{ item.cpuload1 }}</td>
        <td style="color: #327AE6; font-weight: bold">CPU15</td>
        <td>{{ item.cpuload15 }}</td>
        <td style="color: #327AE6; font-weight: bold">CPU5</td>
        <td>{{ item.cpuload5 }}</td>
      </tr>
      
      <tr>
        <td style="color: #327AE6; font-weight: bold">CPU核数</td>
        <td>{{ item.cpunums }}</td>
        <td style="color: #327AE6; font-weight: bold">空闲内存</td>
        <td>{{ item.freeram }}</td>
        <td style="color: #327AE6; font-weight: bold">共享内存</td>
        <td>{{ item.sharedram }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">温度</td>
        <td>{{ item.temp }}</td>
        <td style="color: #327AE6; font-weight: bold">总磁盘</td>
        <td>{{ item.totaldisk }}</td>
        <td style="color: #327AE6; font-weight: bold">总内存</td>
        <td>{{ item.totalram }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">运行时间</td>
        <td>{{ item.uptime }}</td>
        <td style="color: #327AE6; font-weight: bold">已用磁盘</td>
        <td>{{ item.usedisk }}</td>
        <td style="color: #327AE6; font-weight: bold">计算能力</td>
        <td>{{ item.tops }}</td>
      </tr>
    </table>

    <br />
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./tsysteminfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { Delete, Edit, Search, Share, Upload } from "@element-plus/icons-vue";
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/tsysteminfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tsysteminfo/export",
  deleteURL: "/edge/tsysteminfo",
  deleteIsBatch: true,
  dataForm: {
    id: "",
    nodeId: ""
  }
});
//根据id获取详情
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const dialogTableVisible = ref(false);
const gridData = ref();
//查看详情
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : []; // 包装在数组中
  dialogTableVisible.value = true;
};
const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
<style scoped>
    table {
  width: 100%; /* 设置表格宽度为100% */
  max-width: 1200px; /* 设置表格的最大宽度 */
  border-collapse: collapse; /* 使用合并边框 */
  table-layout: fixed; /* 固定布局 */
}
th,
td {
  padding: 10px; /* 单元格内边距 */
  overflow: auto; /* 溢出隐藏 */
  white-space: nowrap; /* 不换行 */
}
</style>