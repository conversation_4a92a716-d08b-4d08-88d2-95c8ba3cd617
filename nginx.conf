user  nginx;
worker_processes  auto;

error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    server {
            listen 80;
            server_name localhost;

            location / {
              root   /usr/share/nginx/html;
              index  index.html index.htm;
              try_files $uri $uri/ /index.html;
            }

            location /captcha {
                 proxy_pass http://edge-computing-admin-server:9080;
            }

            location /login {
                 proxy_pass http://edge-computing-admin-server:9080;
            }

            location /logout {
                 proxy_pass http://edge-computing-admin-server:9080;
            }

            location /edge {
                 proxy_pass http://edge-computing-admin-server:9080;
            }

            location /sys {
                 proxy_pass http://edge-computing-admin-server:9080;
            }

#             location /index {
#                  proxy_pass http://zlm:35007;
#             }

#             location /live {
#                  proxy_pass http://zlm:35007;
#             }
# 拉流播放
            location /live/ {
                proxy_pass http://*************:8088;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'Origin, Content-Type, Accept, Authorization';
            }
#             推流管理工具
            location /video {
                 proxy_pass http://localhost:8899;
            }

            location /api/v1/upload {
                 proxy_pass http://common-files.common.svc.cluster.local:9080;
                 client_max_body_size  1000m;
            }

    }
    # 注意要添加这一行
    # include /etc/nginx/conf.d/*.conf;
}

stream {
  server {
      listen 1935;
      proxy_pass *************:1935; # RTSP服务器地址
  }
}
