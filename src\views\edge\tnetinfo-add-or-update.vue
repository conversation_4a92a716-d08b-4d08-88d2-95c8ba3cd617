<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.onOrOff')" prop="ipower">
        <el-switch v-model="isPower"></el-switch>
      </el-form-item>
      <el-form-item :label="$t('edge.nodeID')" prop="nodeId">
        <el-input disabled v-model="dataForm.nodeId" :placeholder="$t('edge.nodeID')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.interfaceName')" prop="sinterface">
        <el-input disabled v-model="dataForm.sinterface" :placeholder="$t('edge.interfaceName')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.chooseIPv4Method')" prop="sv4Method">
        <el-select v-model="dataForm.sv4Method" :placeholder="$t('edge.selectActiveArea')">
          <el-option :label="$t('edge.autoGet')" value="dhcp"></el-option>
          <el-option :label="$t('edge.manualConfig')" value="manual"></el-option>
          <el-option :label="$t('edge.unknown')" value=""></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('edge.ipv4Address')" prop="sv4Address">
        <el-input v-model="dataForm.sv4Address" :placeholder="$t('edge.ipv4Address')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.ipv4SubnetMask')" prop="sv4Netmask">
        <el-input v-model="dataForm.sv4Netmask" :placeholder="$t('edge.ipv4SubnetMask')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.ipv4Gateway')" prop="sv4Gateway">
        <el-input v-model="dataForm.sv4Gateway" :placeholder="$t('edge.ipv4Gateway')"></el-input>
      </el-form-item>

      <el-form-item :label="$t('edge.physicalAddress')" prop="saddress">
        <el-input disabled v-model="dataForm.saddress" :placeholder="$t('edge.physicalAddress')"></el-input>
      </el-form-item>

      <el-form-item label="DNS1" prop="sdns1">
        <el-input v-model="dataForm.sdns1" placeholder="DNS1"></el-input>
      </el-form-item>
      <el-form-item label="DNS2" prop="sdns2">
        <el-input v-model="dataForm.sdns2" placeholder="DNS2"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const isPower = ref(false);
const dataForm = reactive({
  id: "",
  sv4Address: "",
  sv4Gateway: "",
  sv4Method: "",
  sv4Netmask: "",
  iduplex: "",
  iNicSpeed: "",
  ipower: "",
  saddress: "",
  sdns1: "",
  sdns2: "",
  sinterface: "",
  snicSpeed: "",
  snicSpeedSupport: "",
  nodeId: ""
});

const rules = ref({
  sv4Address: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Gateway: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Method: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Netmask: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  saddress: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sinterface: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  nodeId: [
    {
      required: $t("validate.required"),
      trigger: "blur"
    }
  ]
});
const initForm = initDataForm(dataForm);
const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/tnetinfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tnetinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
<style>
</style>
