FROM harbor.mybi.top:9180/common/node:18.20.8 as builder

WORKDIR /opt/app

COPY . .

RUN npm install --registry=https://registry.npmmirror.com --legacy-peer-deps
#RUN npm install --registry=https://registry.npmmirror.com --force

RUN npm run build


FROM harbor.mybi.top:9180/common/nginx:latest

COPY --from=builder /opt/app/dist /usr/share/nginx/html/

COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
