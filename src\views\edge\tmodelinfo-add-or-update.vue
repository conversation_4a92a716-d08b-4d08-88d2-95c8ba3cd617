<template>
  <el-dialog align-center v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.modelName')" prop="sModelName">
        <el-input v-model="dataForm.smodelName" :placeholder="$t('edge.modelName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.modelFileName')" prop="sModelFileName">
        <el-input v-model="dataForm.smodelFileName" :placeholder="$t('edge.modelFileName')"></el-input>
        <el-upload class="upload-demo" :action="url" :show-file-list="false" :on-success="handleModelFileSuccess" :before-upload="beforeUpload">
          <el-button size="small" type="primary">{{ $t("edge.selectModel") }}</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item :label="$t('edge.modelSize')" prop="iInputSize">
        <el-input v-model="dataForm.iinputSize" :placeholder="$t('edge.modelSize')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.labelFileName')" prop="sLabelFile">
        <el-input v-model="dataForm.slabelFile" :placeholder="$t('edge.labelFileName')"></el-input>
        <el-upload class="upload-demo" :action="url" :show-file-list="false" :on-success="handleModelFileSuccess">
          <el-button size="small" color="rgba(50,122,230,1)">{{ $t("edge.labelModel") }}</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import app from "@/constants/app";
import initDataForm from "@/utils/initDataForm";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const url = ref(app.data_api + "/api/v1/upload");
const visible = ref(false);
const uploading = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  smodelName: "",
  smodelFileName: "",
  iinputSize: 0,
  slabelFile: ""
});
const initForm = initDataForm(dataForm);
const rules = ref({
  ipAddr: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  uuid: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  camId: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  streamUrl: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});
const beforeUpload = () => {
  // 在上传之前设置全屏loading状态为显示
  if (uploading.value) return false;
  uploading.value = true;
  return true; // 返回true表示继续上传，false表示取消上传
};
const handleModelFileSuccess = (data: any) => {
  if (data && data.code === 0) {
    const currentHostname = window.location.hostname;
    // 获取当前端口号（port）
    const currentPort = window.location.port;
    const currentURL = window.location.protocol + "//" + currentHostname + (currentPort ? ":" + currentPort : "");
    dataForm.smodelFileName = currentURL + data.data.url;
    uploading.value = false;
    ElMessage({
      type: "success",
      message: "文件上传成功!"
    });
  } else {
    ElMessage({
      type: "error",
      message: "文件上传失败!"
    });
  }
};
const init = (id?: string) => {
  visible.value = true;
  dataForm.id = "";
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }
  if (id) {
    getInfo(id);
  }
};
// 获取信息
const getInfo = (id: string) => {
  baseService.get("/edge/tmodelinfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
  });
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tmodelinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
