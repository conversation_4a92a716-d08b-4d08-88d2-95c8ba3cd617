<template>
  <el-drawer class="rr-setting-wrap" v-model="isShow" :before-close="handleClose" :destroy-on-close="true" title="主题设置" modal :size="300">
    <div class="rr-setting">
      <div class="el-space el-space--vertical rr-theme" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" @click="changeSiderColor(item)" v-for="item in siderColor" style="padding-bottom: 0px; margin-right: 8px">
              <el-tooltip class="box-item" effect="dark" :content="item.tooltip" placement="top-start">
                <span :class="[item.color, { active: item.active }]" class="card side el-tooltip__trigger el-tooltip__trigger"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card header light el-tooltip__trigger el-tooltip__trigger"></span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card header dark el-tooltip__trigger el-tooltip__trigger"></span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card header primary active el-tooltip__trigger el-tooltip__trigger"></span></div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 8px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="flex-wrap: wrap; margin-bottom: -2px; align-items: center">
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(64, 158, 255)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(11, 178, 212)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(62, 142, 247)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(17, 194, 109)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color active el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(23, 179, 163)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(102, 122, 250)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(153, 123, 113)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(148, 99, 247)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(117, 117, 117)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(235, 103, 9)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(247, 69, 132)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(252, 185, 0)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(255, 76, 82)"></span></div>
            <div class="el-space__item" style="padding-bottom: 2px; margin-right: 2px"><span class="color el-tooltip__trigger el-tooltip__trigger" style="background-color: rgb(20, 20, 20)"></span></div>
          </div>
        </div>
      </div>
      <div class="el-divider el-divider--horizontal" role="separator" style="--el-border-style: solid"><!--v-if--></div>
      <div class="el-space el-space--vertical rr-theme" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px"><span class="rr-setting-title text-2">布局模式</span></div>
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px">
          <div class="el-space el-space--horizontal" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card navlayout side dark active el-tooltip__trigger el-tooltip__trigger"></span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card navlayout header dark el-tooltip__trigger el-tooltip__trigger"></span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span class="card navlayout header dark mix el-tooltip__trigger el-tooltip__trigger"></span></div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 15px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>内容区域铺满</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="isContentCover" />
            </div>
          </div>
        </div>
      </div>
      <div class="el-divider el-divider--horizontal" role="separator" style="--el-border-style: solid"><!--v-if--></div>
      <div class="el-space el-space--vertical rr-other" style="align-items: flex-start">
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px"><span class="rr-setting-title text-2">其他配置</span></div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>固定Logo栏</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="isContentCover" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>侧栏彩色图标</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="isContentCover" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>侧栏排他展开</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="isContentCover" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>启用标签页</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-switch v-model="isContentCover" />
            </div>
          </div>
        </div>
        <div class="el-space__item" style="padding-bottom: 16px; margin-right: 0px">
          <div class="el-space el-space--horizontal rr-switch" style="align-items: center">
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px"><span>标签显示风格</span></div>
            <div class="el-space__item" style="padding-bottom: 0px; margin-right: 8px">
              <el-select v-model="tagStyle" class="m-2" placeholder="默认" style="max-width: 80px">
                <el-option v-for="item in tagSelect" :key="item.id" :label="item.name" :value="item.value" />
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { themeSetting } from "@/constants/config";
import { EMitt, ESidebarLayoutEnum, EThemeColor, EThemeSetting } from "@/constants/enum";
import { getThemeConfigCache, setThemeColor, updateTheme, getThemeConfigCacheByKey, getThemeConfigToClass } from "@/utils/theme";
import emits from "@/utils/emits";
import { useAppStore } from "@/store";
import { useMediaQuery } from "@vueuse/core";
import { useRouter, RouteRecordRaw } from "vue-router";
import { getValueByKeys } from "@/utils/utils";
let isShow = ref(false);
const siderColor = ref([
  {
    color: "dark",
    tooltip: "暗色侧边栏",
    active: themeSetting.sidebar === "dark"
  },
  {
    color: "light",
    tooltip: "亮色侧边栏",
    active: themeSetting.sidebar === "light"
  }
]);
const tagSelect = ref([
  { id: 1, name: "默认", value: "default" },
  { id: 2, name: "原点", value: "round" },
  { id: 3, name: "卡片", value: "card" }
]);
const tagStyle = ref("");
const isContentCover = ref(false);
const isMobile = useMediaQuery("(max-width: 768px)");
const themeCache = getThemeConfigCache();
const sidebarLayoutCache = getThemeConfigCacheByKey(EThemeSetting.NavLayout, themeCache);
const router = useRouter();
const store = useAppStore();
const state = reactive({
  isShowNav: sidebarLayoutCache !== ESidebarLayoutEnum.Top,
  sidebarLayout: sidebarLayoutCache,
  themeClass: getThemeConfigToClass(themeCache),
  loading: false,
  mixLayoutRoutes: router.options.routes.find((x: RouteRecordRaw) => x.path === "/")?.children ?? ([] as RouteRecordRaw[])
});
const changeSiderColor = (siderItem: any) => {
  if (siderItem.color === themeSetting.sidebar) return;
  themeSetting.sidebar = siderItem.color;
  emits.on(EMitt.OnSetTheme, ([type, value]) => {
    state.themeClass[type] = "ui-" + value;
  });
  emits.on(EMitt.OnSetNavLayout, (vl) => {
    state.sidebarLayout = vl;
    state.isShowNav = vl !== ESidebarLayoutEnum.Top;
    if (vl === ESidebarLayoutEnum.Mix) {
      const currRoute = getValueByKeys(getValueByKeys(router.currentRoute.value.meta, "matched", [])[0], "path", "");
      state.mixLayoutRoutes = store.state.routes.find((x: RouteRecordRaw) => x.path === currRoute)?.children ?? [];
    }
  });
  emits.on(EMitt.OnLoading, (vl) => {
    state.loading = vl;
  });
};
const isOpen = () => {
  isShow.value = true;
};
const isClose = () => {
  isShow.value = false;
};
defineExpose({
  isOpen,
  isClose
});
const handleClose = () => {
  isClose();
};
</script>

<style>
.rr-setting {
  padding: 20px;
}

.rr-setting .el-divider {
  margin: 20px 0;
}
.el-drawer__header .el-drawer__title {
  font-size: 1.5em;
}
.rr-setting-wrap .el-drawer__header {
  color: #595959;
  font-size: 15px;
  margin-bottom: 0;
  padding: 13px 16px;
  border-bottom: 1px solid #f4f4f4;
}

.rr-setting-wrap .el-drawer__body {
  overflow: auto;
  padding: 0;
}

.rr-setting-title {
  font-size: 13px;
}

.rr-setting .rr-theme .card {
  width: 50px;
  height: 35px;
  border-radius: 3px;
  margin: 0 20px 20px 0;
  background-color: #f5f7fa;
  box-shadow: 0 1px 3px #00000026;
  display: inline-block;
  vertical-align: top;
  position: relative;
  cursor: pointer;
}

.rr-setting .rr-theme .card.side:before {
  content: "";
  width: 15px;
  height: 100%;
  background-color: #fff;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  display: inline-block;
  vertical-align: top;
}

.rr-setting .rr-theme .card.side.dark:before {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.header:before {
  content: "";
  border-top-left-radius: 3px;
  display: inline-block;
  vertical-align: top;
  width: 100%;
  height: 10px;
  background-color: #fff;
  border-bottom-left-radius: 0;
  border-top-right-radius: 3px;
}

.rr-setting .rr-theme .card.header.light:before {
  width: 100%;
  height: 10px;
  background-color: #fff;
  border-bottom-left-radius: 0;
  border-top-right-radius: 3px;
}

.rr-setting .rr-theme .card.header.dark:before {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.header.primary:before {
  background-color: #409eff;
}

.rr-setting .rr-theme .card.mix {
  background-color: #2e3549;
}

.rr-setting .rr-theme .card.mix.dark:before {
  background-color: #f0f2f5;
  width: 35px;
  height: 25px;
  position: absolute;
  bottom: 0;
  right: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 3px;
}

.rr-setting .rr-theme .card.side.active:after,
.rr-setting .rr-theme .card.header.active:after,
.rr-setting .rr-theme .card.mix.active:after {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #19be6b;
  position: absolute;
  left: 50%;
  bottom: -15px;
  margin-left: -3px;
}

.rr-setting .rr-theme .color {
  width: 20px;
  height: 20px;
  margin: 8px 8px 0 0;
  border-radius: 2px;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0 0 0, 0.1);
  vertical-align: top;
  position: relative;
  cursor: pointer;
}

.rr-setting .rr-theme .color.active:after {
  content: url('data:image/svg+xml;charset=utf-8,<svg width="14" height="14" color="rgb(255 255 255)" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-042ca774=""><path fill="currentColor" d="M406.656 706.944L195.84 496.256a32 32 0 10-45.248 45.248l256 256 512-512a32 32 0 00-45.248-45.248L406.592 706.944z"></path></svg>');
  font-family: element-icons !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -7px 0 0 -7px;
  font-size: 14px;
  color: #fff;
}

.rr-setting .rr-theme,
.rr-setting .rr-other {
  width: 100%;
}

.rr-setting .rr-theme > .el-space__item,
.rr-setting .rr-other > .el-space__item {
  width: 100%;
}

.rr-setting .rr-switch {
  justify-content: space-between;
  width: 100%;
}
</style>
