<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="" prop="snapshotId">
        <el-input v-model="dataForm.snapshotId" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="path">
        <el-input v-model="dataForm.path" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="type">
        <el-input v-model="dataForm.type" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="device">
        <el-input v-model="dataForm.device" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="total">
        <el-input v-model="dataForm.total" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="free">
        <el-input v-model="dataForm.free" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="used">
        <el-input v-model="dataForm.used" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="usedPercent">
        <el-input v-model="dataForm.usedPercent" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="inodesTotal">
        <el-input v-model="dataForm.inodesTotal" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="inodesUsed">
        <el-input v-model="dataForm.inodesUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="inodesFree">
        <el-input v-model="dataForm.inodesFree" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="inodesUsedPercent">
        <el-input v-model="dataForm.inodesUsedPercent" placeholder=""></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  snapshotId: '',  path: '',  type: '',  device: '',  total: '',  free: '',  used: '',  usedPercent: '',  inodesTotal: '',  inodesUsed: '',  inodesFree: '',  inodesUsedPercent: ''});

const rules = ref({
          snapshotId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          path: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          type: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          device: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          total: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          free: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          used: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          usedPercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          inodesTotal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          inodesUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          inodesFree: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          inodesUsedPercent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/dashboarddiskinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/dashboarddiskinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
