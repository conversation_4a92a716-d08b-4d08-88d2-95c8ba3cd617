<template>
  <div class="mod-edge__dashboardsnapshot">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
<!--      <el-form-item>-->
<!--        <el-button type="primary" @click="addOrUpdateHandle()">新增-->
<!--        </el-button>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border
              @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--      <el-table-column prop="id" label="快照主键" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="hostname" label="主机名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="hostId" label="UUID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="os" label="操作系统" header-align="center" align="center"></el-table-column>
      <el-table-column prop="platform" label="平台" header-align="center" align="center"></el-table-column>
      <el-table-column prop="platformFamily" label="平台家族" header-align="center" align="center"></el-table-column>
      <el-table-column prop="platformVersion" label="平台版本" header-align="center" align="center"></el-table-column>
      <el-table-column prop="kernelArch" label="内核架构" header-align="center" align="center"></el-table-column>
      <el-table-column prop="kernelVersion" label="内核版本" header-align="center" align="center"></el-table-column>

      <el-table-column prop="ipv4Addr" label="IPv4" header-align="center" align="center"></el-table-column>
      <el-table-column prop="systemProxy" label="系统代理" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuCores" label="物理CPU核心数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuLogicalCores" label="逻辑CPU核心数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuModelName" label="CPU型号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="uptime" label="运行秒数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="timeSinceUptime" label="启动以来的时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="procs" label="进程数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="load1" label="1min 负载" header-align="center" align="center"></el-table-column>
      <el-table-column prop="load5" label="5min 负载" header-align="center" align="center"></el-table-column>
      <el-table-column prop="load15" label="15min 负载" header-align="center" align="center"></el-table-column>
      <el-table-column prop="loadUsagePercent" label="负载使用率" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuUsedPercent" label="CPU 总使用率" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuUsed" label="CPU 已使用量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cpuTotal" label="CPU 总核数" header-align="center" align="center"></el-table-column>

      <el-table-column prop="memoryTotal" label="内存总量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="memoryAvailable" label="可用内存" header-align="center" align="center"></el-table-column>
      <el-table-column prop="memoryUsed" label="已用内存" header-align="center" align="center"></el-table-column>
      <el-table-column prop="memoryUsedPercent" label="内存使用率" header-align="center" align="center"></el-table-column>
      <el-table-column prop="swapTotal" label="交换分区总量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="swapAvailable" label="可用交换分区" header-align="center" align="center"></el-table-column>
      <el-table-column prop="swapUsed" label="已用交换分区" header-align="center" align="center"></el-table-column>
      <el-table-column prop="swapUsedPercent" label="交换分区使用率" header-align="center" align="center"></el-table-column>

      <el-table-column prop="ioReadBytes" label="累计读字节" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ioWriteBytes" label="累计写字节" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ioCount" label="IO 操作数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ioReadTime" label="读耗时(ms)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ioWriteTime" label="写耗时(ms)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="netBytesSent" label="网络发送字节" header-align="center" align="center"></el-table-column>
      <el-table-column prop="netBytesRecv" label="网络接收字节" header-align="center" align="center"></el-table-column>
      <el-table-column prop="shotTime" label="快照时间戳(ms)" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="createdAt" label="" header-align="center" align="center"></el-table-column>-->

      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
<!--          <el-button  type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit"
                   :total="state.total" layout="total, sizes, prev, pager, next, jumper"
                   @size-change="state.pageSizeChangeHandle"
                   @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import {reactive, ref, toRefs} from "vue";
import AddOrUpdate from "./dashboardsnapshot-add-or-update.vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/dashboardsnapshot/page",
  getDataListIsPage: true,
  exportURL: "/edge/dashboardsnapshot/export",
  deleteURL: "/edge/dashboardsnapshot"
});

const state = reactive({...useView(view), ...toRefs(view)});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
