<template>
  <!-- 检测数据 -->
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <!-- <el-form-item>
        <el-input v-model="state.dataForm.id" style="width: 200px" placeholder="id" clearable></el-input>
      </el-form-item> -->
      <el-form-item>
        <!-- <el-tooltip :content="$t('edge.securityLevelContent')" placement="bottom" effect="customized"> -->
        <div class="inputs">
          <!-- <el-input v-model="state.dataForm.securityLevel" style="width: 200px" :placeholder="$t('edge.alertLevel')" clearable></el-input> -->
          <el-input clearable v-model="state.dataForm.deviceId" placeholder="摄像头"></el-input>
        </div>
        <template #content>
          <div>{{ $t("edge.securityLevelContent") }}</div>
        </template>
        <!-- </el-tooltip> -->
      </el-form-item>

      <el-form-item>
        <!-- <el-tooltip :content="$t('edge.securityLevelContent')" placement="bottom" effect="customized"> -->
        <div class="inputs">
          <!-- <el-input v-model="state.dataForm.securityLevel" style="width: 200px" :placeholder="$t('edge.alertLevel')" clearable></el-input> -->
          <el-select v-model="state.dataForm.securityLevel" clearable placeholder="告警类型" style="width: 240px">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <template #content>
          <div>{{ $t("edge.securityLevelContent") }}</div>
        </template>
        <!-- </el-tooltip> -->
      </el-form-item>
      <el-form-item>
        <div class="block">
          <el-date-picker v-model="time" type="daterange" @change="toString" range-separator="--" start-placeholder="起始日期" end-placeholder="结束日期" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()"></el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item> -->
      <el-form-item label="警报开关">
        <el-switch v-model="isPollingActive" active-text="开启" inactive-text="关闭"></el-switch>
      </el-form-item>
    </el-form>
    <div style="width: 100%; display: flex; flex-wrap: wrap; justify-content: left; align-items: center">
      <el-card class="card" shadow="hover" @click="details(item.id)" v-for="item in state.dataList" :key="item.id">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <el-tag @click.stop="" :style="{ backgroundColor: getStyle(item) }" style="margin-bottom: 10px; color: white; border-radius: 100px; font-size: 12px; padding: 5px 10px">{{ getSecurityLevelText(item.securityLevel) }}</el-tag>
          <el-button style="margin-top: -10px; " size="small" plain type="" @click.stop="" @click="showSeries(item.id)">列表数据</el-button>
          <el-button style="margin-top: -10px" size="small" plain type="danger" @click.stop="state.deleteHandle(item.id)">删除</el-button>
        </div>
        <img :src="getImageUrl(item.storagePath)" style="width: 100%" />
        <div>
          <el-icon style="display: relative; top: 2px; margin-right: 5px"><Camera /></el-icon>报警设备: {{ item.deviceId }}
        </div>
        <div>
          <el-icon style="display: relative; top: 2px; margin-right: 5px"><Clock /></el-icon>报警时间: {{ formatTimestamp(item.detectTime) }}
        </div>
      </el-card>
    </div>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <show-data-list :dialogVisible="isShowDataList" :dataList="showLists" @close="closeDialogHandle"></show-data-list>
  </div>

  <el-dialog v-model="dialogTableVisible" width="800">
    <template #header>
      <img :src="waringLogo" alt="" style="width: 50px; height: 30px" />
      <div style="display: inline-block; font-size: 20px; float: right; margin-top: 2px; margin-right: 610px">告警详情</div>
    </template>
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100%" style="height: 200px; text-align: center" v-for="item in gridData" :key="item.id">
      <tr>
        <td style="color: #327ae6; font-weight: bold">ID</td>
        <td>{{ item.id }}</td>
        <td style="color: #327ae6; font-weight: bold">文件格式</td>
        <td>{{ item.fileFormat }}</td>
        <td style="color: #327ae6; font-weight: bold">部署地点经度</td>
        <td>{{ item.shotPlaceLongitude }}</td>
      </tr>
      <tr>
        <td style="color: #327ae6; font-weight: bold">序列号</td>
        <td>{{ item.seq }}</td>
        <td style="color: #327ae6; font-weight: bold">时间排序</td>
        <td>{{ item.eventSort }}</td>
        <td style="color: #327ae6; font-weight: bold">部署地点纬度</td>
        <td>{{ item.shotPlaceLatitude }}</td>
      </tr>
      <tr>
        <td style="color: #327ae6; font-weight: bold">设备ID</td>
        <td>{{ item.deviceId }}</td>
        <td style="color: #327ae6; font-weight: bold">告警类型</td>
        <td>{{ getSecurityLevelText(item.securityLevel) }}</td>
        <td style="color: #327ae6; font-weight: bold">摄像头索引码</td>
        <td>{{ item.cameraIndexCode }}</td>
      </tr>
      <tr>
        <td style="color: #327ae6; font-weight: bold">关键字</td>
        <td>{{ item.keyword }}</td>
        <td style="color: #327ae6; font-weight: bold">内容描述</td>
        <td>{{ item.contentDescription }}</td>
        <td style="color: #327ae6; font-weight: bold">异常行为</td>
        <td>{{ item.behaviorDescription }}</td>
      </tr>
      <tr>
        <td style="color: #327ae6; font-weight: bold">视频ID</td>
        <td>{{ item.videoId }}</td>
        <td style="color: #327ae6; font-weight: bold">部署地点</td>
        <td>{{ item.shotPlaceFullAddress }}</td>
        <td style="color: #327ae6; font-weight: bold">检测时间</td>
        <td>{{ formatTimestamp(item.detectTime) }}</td>
      </tr>
    </table>
    <br />
    <div style="display: flex; justify-content: space-around">
      <div>
        <div style="margin-left: 80px; margin-bottom: 20px; color: #327ae6; font-weight: bold">原始图片地址</div>
        <div>
          <el-image :src="getImageUrl(gridData[0].storagePathOrg)" preview-teleported :preview-src-list="[getImageUrl(gridData[0].storagePathOrg)]" style="width: 200px; height: 130px; margin-left: 20px" />
        </div>
      </div>
      <div>
        <div style="margin-left: 80px; margin-bottom: 20px; color: #327ae6; font-weight: bold">画框图片地址</div>
        <div>
          <el-image :src="getImageUrl(gridData[0].storagePath)" preview-teleported :preview-src-list="[getImageUrl(gridData[0].storagePath)]" style="width: 200px; height: 130px; margin-left: 20px" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, computed } from "vue";
import useView from "@/hooks/useView";
import { globalLanguage } from "@/utils/globaLang";
import { formatTimestamp } from "@/utils/format";
import showDataList from "./show-data-list.vue";
import { Delete, Edit, Search, Share, Upload } from "@element-plus/icons-vue";
import { watch } from "vue";
import waringLogo from "@/assets/images/waringLogo.gif";
import { Action, ElMessage, ElMessageBox } from "element-plus";
import { IObject } from "@/types/interface";
import baseService from "@/service/baseService";
import { AlarmTypeInfo } from "./alarmtype-add-or-update.vue";

const alarmTypeInfo = ref<AlarmTypeInfo[]>([]);
const isPollingActive = ref(localStorage.getItem("isPollingActive") === null ? true : localStorage.getItem("isPollingActive") === "true");

// 加载安全预警类型信息
baseService.get("/edge/tmodelcategory/allByType", { type: "CV" }).then((res) => {
  alarmTypeInfo.value = res.data.list;
});
watch(isPollingActive, (newValue) => {
  localStorage.setItem("isPollingActive", newValue.toString());
});
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/detection/page",
  getDataListIsPage: true,
  exportURL: "/edge/detection/export",
  deleteURL: "/edge/detection",
  deleteIsBatch: true,
  dataForm: {
    id: "",
    securityLevel: "",
    deviceId: "",
    startTime: "",
    stopTime: ""
  },
  orderField: "timestamp"
});
const showSeries = (id: number) => {
  const detail = getDetail(id);
  if (detail !== undefined) {
    showLists.value = detail;
  } else {
    // 处理获取详情失败的情况，例如设置为一个空对象或显示错误信息
    showLists.value = {};
  }
  isShowDataList.value = true;
};

const state = reactive({ ...useView(view), ...toRefs(view) });
const isShowDataList = ref(false);
const showLists = ref({});
const showConfigDataList = (row: Object) => {
  isShowDataList.value = true;
  showLists.value = row;
};
const getStyle = (item: IObject) => {
  const securityLevelTexts: any = {
    "1": "#e6a33e",
    "2": "#e6a33e",
    "3": "#e6a33e",
    "4": "#e6a33e",
    "5": "#e6a33e",
    "6": "#e6a33e",
    "7": "#e6a33e",
    "8": "#e6a33e",
    "9": "#e6a33e",
    "10": "#e6a33e",
    "11": "#e6a33e",
    "12": "#e6a33e",
    "13": "#e6a33e",
    "14": "#e6a33e",
    "15": "#e6a33e",
    "16": "#e6a33e",
    "17": "#e6a33e",
    "18": "#e6a33e",
    "19": "#e6a33e",
    "20": "#e6a33e",
    "21": "#e6a33e",
    "22": "#e6a33e",
    "23": "#e6a33e",
    "24": "#e6a33e",
    "25": "#e6a33e",
    "26": "#e6a33e",
    "27": "#e6a33e",
    "28": "#e6a33e",
    "29": "#e6a33e",
    "30": "#e6a33e",
    "31": "#e6a33e",
    "32": "#e6a33e",
    "33": "#e6a33e",
    "34": "#e6a33e",
    "35": "#e6a33e",
    "36": "#e6a33e",
    "37": "#e6a33e",
    "38": "#e6a33e",
    "39": "#e6a33e",
    "40": "#e6a33e",
    "41": "#e6a33e",
    "42": "#e6a33e",
    "43": "#e6a33e",
    "44": "#e6a33e",
    "45": "#e6a33e",
    "46": "#e6a33e",
    "49": "#e6a33e"
  };
  return securityLevelTexts[item.securityLevel];
};
const closeDialogHandle = () => {
  isShowDataList.value = false;
  showLists.value = {};
};
//根据id获取详情
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const dialogTableVisible = ref(false);
const gridData = ref();
//查看详情
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : []; // 包装在数组中
  dialogTableVisible.value = true;
  console.log(gridData.value);
};

const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const getImageUrl = (path: string) => {
  // 检查是否在本地环境
  if (process.env.NODE_ENV === "development") {
    // 直接返回本地路径
    return path;
  } else {
    // 生产环境下，使用完整的URL
    if (typeof window !== "undefined") {
      const filename: string = path.split("/").pop() as string;
      return `${window.location.origin}/api/v1/upload/${filename}`;
    }
    return ""; // 或返回一个默认图片路径
  }
  // if (typeof window !== 'undefined') {
  //   const filename: string = path.split('/').pop() as string;  // 确保pop()返回的不是undefined
  //   return `${window.location.origin}/api/v1/upload/${filename}`;
  // }
  // return ''; // 或者返回一个默认图片路径
};

//告警类型下拉
const value = ref("");
const time = ref("");
const toString = (val: any) => {
  state.dataForm.startTime = "";
  state.dataForm.stopTime = "";
  state.dataForm.startTime = new Date(val[0]).getTime().toString();
  state.dataForm.stopTime = new Date(val[1]).getTime().toString();
};

// 动态生成 options 数组
const options = computed(() => {
  if (alarmTypeInfo.value && alarmTypeInfo.value.length > 0) {
    return alarmTypeInfo.value.map(item => ({
      value: item.securityLevel || "",
      label: item.describeInfo || `类型${item.securityLevel}`
    }));
  }
  
  // 如果动态数据不可用，使用硬编码的备用数据
  return [
    { value: "1", label: "异常火点" },
    { value: "2", label: "异常烟雾" },
    { value: "3", label: "违规闯入" },
    { value: "4", label: "人员脱岗" },
    { value: "5", label: "人员摔倒" },
    { value: "6", label: "人员超限" },
    { value: "7", label: "人员趴睡" },
    { value: "8", label: "安全帽佩戴" },
    { value: "9", label: "工服穿戴" },
    { value: "10", label: "不按规定车道行驶" },
    { value: "11", label: "道路垃圾识别" },
    { value: "12", label: "车辆拥堵识别" },
    { value: "13", label: "车牌识别" },
    { value: "14", label: "机动车违停" },
    { value: "15", label: "车辆压线" },
    { value: "16", label: "车辆逆行" },
    { value: "17", label: "人员徘徊" },
    { value: "18", label: "偷倒渣土" },
    { value: "19", label: "雨水池污水口排放识别" },
    { value: "20", label: "车辆抛洒垃圾等杂物" },
    { value: "21", label: "人员攀爬" },
    { value: "22", label: "渣土车无顶盖、篷布识别" },
    { value: "23", label: "车辆超载违规" },
    { value: "24", label: "管廊管道有异物遮盖识别" },
    { value: "25", label: "危化品车辆识别" },
    { value: "26", label: "抽烟识别" },
    { value: "27", label: "打电话识别" },
    { value: "28", label: "玩手机" },
    { value: "29", label: "跌倒" },
    { value: "30", label: "洗手" },
    { value: "31", label: "拍照" },
    { value: "32", label: "消防通道占用" },
    { value: "33", label: " 通用文字识别" },
    { value: "42", label: "人流量统计" },
    { value: "43", label: " 车流量统计" },
    { value: "44", label: " 机动车流量统计" },
    { value: "45", label: "非机动车流量统计" },
    { value: "46", label: "  人群密度" }
  ];
});

// 动态获取安全预警类型文本
const getSecurityLevelText = (securityLevel: string | number): string => {
  // 优先使用动态加载的 alarmTypeInfo 数据
  if (alarmTypeInfo.value && alarmTypeInfo.value.length > 0) {
    const found = alarmTypeInfo.value.find(item => item.securityLevel === String(securityLevel));
    if (found && found.describeInfo) {
      return found.describeInfo;
    }
  }
  
  // 如果动态数据不可用，使用硬编码的备用数据
  const securityLevelTexts: any = {
    "1": "异常火点",
    "2": "异常烟雾",
    "3": "违规闯入",
    "4": "人员脱岗",
    "5": "人员摔倒",
    "6": "人员超限",
    "7": "人员趴睡",
    "8": "安全帽佩戴",
    "9": "工服穿戴",
    "10": "不按规定车道行驶",
    "11": "道路垃圾识别",
    "12": "车辆拥堵识别",
    "13": "车牌识别",
    "14": "机动车违停",
    "15": "车辆压线",
    "16": "车辆逆行",
    "17": "人员徘徊",
    "18": "偷倒渣土",
    "19": "雨水池污水口排放识别",
    "20": "车辆抛洒垃圾等杂物",
    "21": "人员攀爬",
    "22": "渣土车无顶盖、篷布识别",
    "23": "车辆超载违规",
    "24": "管廊管道有异物遮盖识别",
    "25": "危化品车辆识别",
    "26": "抽烟识别",
    "27": "打电话识别",
    "28": "玩手机",
    "29": "跌倒",
    "30": "洗手",
    "31": "拍照",
    "32": "消防通道占用",
    "33": " 通用文字识别",
    "34": "  中文分词",
    "35": "  关键词提取",
    "36": "文本相似度",
    "37": " 词性标注",
    "38": "情感分析",
    "39": " 实体识别",
    "40": " 文本分类",
    "41": " 文本摘要",
    "42": "人流量统计",
    "43": " 车流量统计",
    "44": " 机动车流量统计",
    "45": "非机动车流量统计",
    "46": "  人群密度",
    "49": "吃喝"
  };
  
  return securityLevelTexts[securityLevel] || `未知类型(${securityLevel})`;
};
</script>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
.el-message-box {
  width: 600px;
  max-width: 5000px;
}
.demo-date-picker {
  display: flex;
  width: 100%;
  padding: 0;
  flex-wrap: wrap;
}

.demo-date-picker .block {
  padding: 30px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  flex: 1;
}

.demo-date-picker .block:last-child {
  border-right: none;
}

.demo-date-picker .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
.card {
  width: 300px;
  cursor: pointer;
  height: 270px;
  margin-top: 20px;
  margin-left: 5px;
}
</style>
