import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import { defineConfig, loadEnv, UserConfig, UserConfigExport } from "vite";
import html from "vite-plugin-html";
import tsconfigPaths from "vite-tsconfig-paths";
import prismjs from "vite-plugin-prismjs";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";

export default (config: UserConfig): UserConfigExport => {
  const mode = config.mode as string;
  return defineConfig({
    base: "./",
    plugins: [
      vue(),
      html({
        inject: {
          injectData: {
            apiURL: loadEnv(mode, process.cwd()).VITE_APP_API,
            dataURL: loadEnv(mode, process.cwd()).VITE_DATAS_URL,
            mediaURL: loadEnv(mode, process.cwd()).VITE_MEDIA_URL,
            mediaSecret: loadEnv(mode, process.cwd()).VITE_MEDIA_SECRET,
            ossURL: loadEnv(mode, process.cwd()).VITE_OSS_API,
            nlpURL: loadEnv(mode, process.cwd()).VITE_NLP_API,
            ocrURL: loadEnv(mode, process.cwd()).VITE_OCR_API,
            miniURL: loadEnv(mode, process.cwd()).VITE_MINI_API,
            videoPushUrl: loadEnv(mode, process.cwd()).VITE_VIDEO_PUSH_API,
            title: ""
          }
        },
        minify: true
      }),
      tsconfigPaths(),
      createSvgIconsPlugin({
        iconDirs: [resolve(__dirname, "src/assets/icons/svg")],
        symbolId: "icon-[dir]-[name]"
      }),
      prismjs({
        languages: ["json", "js"],
        plugins: ["line-numbers"], //配置显示行号插件
        theme: "okaidia", //主题名称
        css: true,
      }),
    ],
    build: {
      chunkSizeWarningLimit: 1024,
      commonjsOptions: {
        include: /node_modules|lib/
      },
      rollupOptions: {
        output: {
          manualChunks: {
            quill: ["quill"],
            lodash: ["lodash"],
            vlib: ["vue", "vue-router", "element-plus"]
          }
        }
      }
    },
    resolve: {
      alias: {
        // 配置别名
        "@": resolve(__dirname, "./src")
      }
    },
    server: {
      open: false, // 自动启动浏览器
      host: "0.0.0.0", // localhost
      port: 8088, // 端口号
      https: false,
      hmr: { overlay: false },
      proxy: {
        // 拉流
        '/live': {
          target: 'http://*************:8088',
          changeOrigin: true
        },
        '/video': {
          target: 'http://localhost:8899',
          changeOrigin: true
        }
      }
    }
  });
};
