<template>
  <div class="mod-edge__imagesinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <!--        <el-button v-if="state.hasPermission('edge:imagesinfo:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
        <!--        <el-button v-if="state.hasPermission('edge:imagesinfo:delete')" type="danger" @click="state.deleteHandle()">删除</el-button>-->
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border
      @selection-change="state.dataListSelectionChangeHandle" show-overflow-tooltip style="width: 100%">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <el-table-column label="ID" header-align="center" align="center" width="100">
        <template #default="scope">
          {{ ((state.page || 1) - 1) * (state.limit || 20) + scope.$index + 1 }}
        </template>
      </el-table-column> 
      <el-table-column prop="imgName" label="算法名" header-align="center" align="center" width="350"></el-table-column>
      <el-table-column prop="imgUrl" label="算法url" header-align="center" align="center">
        <template #default="scope">
          <el-tooltip :content="scope.row.imgUrl" placement="top" :show-after="100">
            <span>{{ truncateText(scope.row.imgUrl, 20) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="platform" label="平台" header-align="center" align="center"></el-table-column>
      <el-table-column prop="type" label="算法类型" header-align="center" align="center"> 
        <template #default="scope">
          {{ getTypeLabel(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="label" label="算法类型标签" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="model" label="模型名称" header-align="center" align="center" width="100"></el-table-column>
      <el-table-column prop="openai_base_url" label="OpenAI Base URL（MLLM）" header-align="center" align="center">
        <template #header>
          <el-tooltip content="OpenAI Base URL（MLLM）" placement="top">
            <span class="table-header-cell">OpenAI Base URL...</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="open_api_key" label="OpenAI API Key（MLLM）" header-align="center" align="center">
        <template #header>
          <el-tooltip content="OpenAI API Key（MLLM）" placement="top">
            <span class="table-header-cell">OpenAI API Key...</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="prompt" label="prompt 提示词（MLLM）" header-align="center" align="center">
        <template #header>
          <el-tooltip content="prompt 提示词（MLLM）" placement="top">
            <span class="table-header-cell">prompt 提示词...</span>
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column prop="memo" label="备注" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="tenantId" label="租户id" header-align="center" align="center"></el-table-column>-->

      <!-- <el-table-column prop="memo" label="备注" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="twfCreated" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="twfModified" label="更新时间" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="twfDeleted" label="删除时间" header-align="center" align="center"></el-table-column> -->
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="warning" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <!--          <el-button v-if="state.hasPermission('edge:imagesinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
          <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
          <!--          <el-button v-if="state.hasPermission('edge:imagesinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit"
      :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle"
      @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import {reactive, ref, toRefs} from "vue";
import AddOrUpdate from "./imagesinfo-add-or-update.vue";
import baseService from "@/service/baseService";
import { AlarmTypeInfo } from "./alarmtype-add-or-update.vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/imagesinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/imagesinfo/export",
  deleteURL: "/edge/imagesinfo"
});

const alarmTypeInfo = ref<AlarmTypeInfo[]>([]);

baseService.get("/edge/tmodelcategory/page", { limit: 100000 }).then((res) => {
  alarmTypeInfo.value = res.data.list;
});

function getTypeLabel(type: string | number) {
  const found = alarmTypeInfo.value.find(item => item.securityLevel == type);
  return found ? found.describeInfo : type;
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

const state = reactive({...useView(view), ...toRefs(view)});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
<style scoped>
.table-header-cell {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>