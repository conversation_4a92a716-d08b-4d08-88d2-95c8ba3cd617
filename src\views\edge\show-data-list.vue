<template>
  <el-dialog align-center :title="title" v-model="props.dialogVisible" width="47%" @close="closeDialog" @open="openDialog">
    <pre class="language-javascript"><code>{{ code }}</code></pre>
  </el-dialog>
</template>

<script setup>
import Prism from "prismjs";
import { ref, onMounted } from "vue";
import { nextTick } from "vue";
const code = ref(null);
const title = ref(null);
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    required: true
  },
  dataList: {
    required: true
  }
});
const emit = defineEmits(["close"]);
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      Prism.highlightAll(
        Prism.languages.javascript, // 使用 JavaScript 语法高亮
        "javascript"
      );
    }, 100);
  });
});

const openDialog = () => {
  if (props.dataList.hasOwnProperty("targetsList")) {
    title.value = "目标列表数据";
    code.value = JSON.stringify(props.dataList.targetsList, null, 2);
  } else if (props.dataList.hasOwnProperty("configs")) {
    title.value = "参数配置";
    code.value = JSON.stringify(props.dataList.configs, null, 2);
  } else {
    code.value = JSON.stringify(props.dataList, null, 2);
  }
};

const closeDialog = () => {
  // 发出关闭对话框的事件
  emit("close");
};
</script>
