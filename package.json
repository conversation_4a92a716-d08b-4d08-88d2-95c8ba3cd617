{"name": "renren-ui", "version": "5.3.0", "private": true, "scripts": {"dev": "vite", "build": "npm run build:prod", "build:prod": "vue-tsc --noEmit && vite build --mode production", "serve": "npm run build && vite preview", "lint": "eslint \"src/**/*.{vue,ts}\" --fix"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"src/**/*.{ts,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vue-flow/background": "^1.3.0", "@vue-flow/core": "^1.33.8", "@vue/runtime-core": "^3.5.13", "@vue/runtime-dom": "^3.5.13", "@vue/server-renderer": "^3.5.13", "@vueuse/core": "9.1.1", "@wangeditor/editor": "5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.27.2", "browser-image-compression": "^2.0.2", "classnames": "^2.3.1", "clipboard": "^2.0.11", "core-js": "^3.14.0", "echarts": "^5.2.2", "element-plus": "2.7.6", "lodash": "^4.17.21", "mitt": "^2.1.0", "nprogress": "^0.2.0", "pinia": "2.0.27", "prismjs": "^1.29.0", "qs": "^6.10.1", "quill": "^1.3.7", "vue": "^3.5.13", "vue-echarts": "^6.0.0", "vue-i18n": "^9.4.1", "vue-router": "4.0.11", "vue3-number-roll-plus": "^0.1.3", "web3": "^4.16.0"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/node": "^22.2.0", "@types/nprogress": "^0.2.0", "@types/prismjs": "^1.26.3", "@types/qs": "^6.9.6", "@types/quill": "^2.0.8", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "4.2.3", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.1", "less-loader": "^10.0.0", "lint-staged": "^11.0.0", "prettier": "^2.6.2", "sass": "^1.50.1", "typescript": "^4.6.3", "video.js": "^8.10.0", "vite": "^6.3.3", "vite-plugin-html": "^2.1.1", "vite-plugin-prismjs": "^0.0.8", "vite-plugin-svg-icons": "2.0.1", "vite-tsconfig-paths": "3.4.0", "vue-tsc": "1.8.8"}}