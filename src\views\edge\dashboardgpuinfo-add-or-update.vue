<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="" prop="snapshotId">
        <el-input v-model="dataForm.snapshotId" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="gpuIndex">
        <el-input v-model="dataForm.gpuIndex" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="productName">
        <el-input v-model="dataForm.productName" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="gpuUtil">
        <el-input v-model="dataForm.gpuUtil" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="temperature">
        <el-input v-model="dataForm.temperature" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="performanceState">
        <el-input v-model="dataForm.performanceState" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="powerUsage">
        <el-input v-model="dataForm.powerUsage" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="powerDraw">
        <el-input v-model="dataForm.powerDraw" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="maxPowerLimit">
        <el-input v-model="dataForm.maxPowerLimit" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memoryUsage">
        <el-input v-model="dataForm.memoryUsage" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memUsed">
        <el-input v-model="dataForm.memUsed" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="memTotal">
        <el-input v-model="dataForm.memTotal" placeholder=""></el-input>
      </el-form-item>
          <el-form-item label="" prop="fanSpeed">
        <el-input v-model="dataForm.fanSpeed" placeholder=""></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  snapshotId: '',  gpuIndex: '',  productName: '',  gpuUtil: '',  temperature: '',  performanceState: '',  powerUsage: '',  powerDraw: '',  maxPowerLimit: '',  memoryUsage: '',  memUsed: '',  memTotal: '',  fanSpeed: ''});

const rules = ref({
          snapshotId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          gpuIndex: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          productName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          gpuUtil: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          temperature: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          performanceState: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          powerUsage: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          powerDraw: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          maxPowerLimit: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memoryUsage: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memUsed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          memTotal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          fanSpeed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/dashboardgpuinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/dashboardgpuinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
