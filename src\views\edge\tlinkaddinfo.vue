<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-edge__tcontroladdinfo}">
        <el-row>
          <el-col type="flex" :xl="16" :lg="17">
            <el-row>
              <el-col>
                <el-col :xl="22" :lg="23">
                  <div
                    style="width: 100%; height: 61.5vh; display: flex; flex-wrap: wrap; background-color: #000;"
                    ref="videoCanvasBox"
                  >
                    <jessibucaPlayer
                      v-if="videoUrl"
                      ref="jessibucaRef"
                      style="flex-grow: 1;"
                      :videoUrl="videoUrl"
                      :error="videoError"
                      :message="videoError"
                      :hasAudio="hasAudio"
                      fluent
                      autoplay
                      live
                    ></jessibucaPlayer>
                    <canvas ref="videoCanvas" class="videoCanvas"></canvas>
                  </div>
                </el-col>
              </el-col>
            </el-row>
            <el-row style="padding-top: 15px">
              <el-row :gutter="4">
                <el-button type="primary" icon="VideoPlay" @click="playStart">
                  {{ $t("edge.playVideo") }}
                </el-button>
                <el-button icon="VideoPause" @click="playStop">
                  {{ $t("edge.stopVideo") }}
                </el-button>
                <el-button icon="Delete" type="danger" :disabled="isDrawing" @click="clearVideoCanvas">
                  {{ $t("edge.clearControlArea") }}
                </el-button>
                <el-button icon="FullScreen" :type="isDrawing ? 'warning' : 'primary'" @click="startDraw">
                  {{ $t("edge.addControlAreaRec") }}
                </el-button>
                <el-popover placement="right" trigger="click" title="布控坐标" :width="400">
                  <template #reference>
                    <el-button slot="reference" icon="Rank" style="margin-left: 10px">
                      {{ $t("edge.viewAreaCoordinates") }}
                    </el-button>
                  </template>
                  <el-input type="textarea" placeholder="请绘制区域后再查看" v-model="coordinatesInfo" autosize></el-input>
                </el-popover>
              </el-row>
            </el-row>
          </el-col>
          <el-col type="flex" :xl="8" :lg="7">
            <el-form label-width="100px" label-position="left" size="small" :model="forms">
              <el-form-item label="选择视频流" prop="videoStream">
                <el-select filterable ref="itemSelect" v-model="forms.selectStreamUrl" placeholder="请选择视频流" @change="setStreamInfo(getSelectedItem().id)">
                  <el-option v-for="item in (state.dataList as any).list" :key="item.streamUrl" :label="item.name" :value="item.streamUrl"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="节点ID">
                <el-select class="m-2" v-model="forms.nodeId" value-key="id"  placeholder="请选择节点ID">
                  <el-option v-for="item in nodeInfo" :key="item.id" :label="item.sname" :value="item.saddress"/>
                </el-select>
              </el-form-item>
              <el-form-item label="链路ID">
                <el-select class="m-2" v-model="dataForms.linkId" value-key="id"  placeholder="请选择链路"  @change="onSelectLinks">
                  <el-option v-for="item in linksInfo" :key="item.id" :label="item.linkName" :value="item.id"/>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('edge.linkName')" prop="linkName">
                <el-input v-model="dataForms.linkName" :placeholder="$t('edge.linkName')"></el-input>
              </el-form-item>
              <el-form-item label="数据推送URL" prop="sendUrl">
                   <el-select  v-model="forms.sname" class="m-2" value-key="id" :placeholder="$t('edge.selectPush')"
                    @change="onSelectPush">
                    <el-option v-for="item in pushInfo" :key="item.id" :label="item.sname" :value="item.id"/>
                    </el-select>
                    <el-input v-model="forms.sendUrl" placeholder="数据推送URL"></el-input>
              </el-form-item>
              <el-form-item label="推送地址">
                <el-input :disabled="true" v-model="forms.selectStreamUrl"></el-input>
              </el-form-item>
              <el-form-item label="设备ID">
                <el-input :disabled="true" v-model="deviceName"></el-input>
              </el-form-item>
              <el-form-item label="摄像头ID">
                <el-input :disabled="true" v-model="cameraId"></el-input>
              </el-form-item>


              <el-form-item label="布控坐标" prop="roi">
                <el-input disabled v-model="forms.roi"></el-input>
              </el-form-item>


              <el-popover
                  placement="bottom-start"
                  title=""
                  :popper-style="popoverStyles"
                  trigger="click"
                >
                  <template #reference>
                    <el-button class="m-2">更多</el-button>
                  </template>
                          <el-form-item label="阈值" prop="threshold">
                        <el-input v-model="forms.threshold"></el-input>
                      </el-form-item>
                      <el-form-item label="时间间隔" prop="timeInter">
                        <el-input v-model="forms.timeInter"></el-input>
                      </el-form-item>
                      <el-form-item label="分析帧" prop="analysisFrame">
                        <el-input v-model="forms.analysisFrame"></el-input>
                      </el-form-item>
                </el-popover>
            </el-form>
            <br>
            <el-button size="small" type="primary" :disabled="isDrawing" @click="dataSubmit">提交</el-button>
            <el-button size="small" @click="mediaListInfo">取消</el-button>
          </el-col>
        </el-row>
    </div>
  </el-card>
</template>

<script lang="ts">
import {defineComponent, ref, onMounted, nextTick, computed} from "vue";
import useView from "@/hooks/useView";
import jessibucaPlayer from "@/views/device/jessibuca.vue";
import Jessibuca from "@/views/device/jessibuca.vue";
import {mediaListInfo, mediaServerRegister} from "@/utils/media";
import {globalLanguage} from "@/utils/globaLang";
import {onUnmounted, reactive, toRefs} from "vue";
import baseService from "@/service/baseService";
import jessibuca from "@/views/device/jessibuca.vue";
import app from "@/constants/app";
import {ElMessage} from "element-plus";
import router from "@/router";

interface Forms {
  selectStreamUrl: string;
  modelUrl: string;
  modelName: string;
  modelBaseName: string;
  sendUrl: string;
  selectModel: string;
  roi: string;
  threshold: string,
  timeInter: string,
  analysisFrame: string,
  trainName: string,
  modelAfterTrainingUrl: string,
  nodeId: string,
  outputStream: string,
  nodeName: string,
  sname: String,
  containerId: string,
  predictPath: string,
}

interface StreamInfo {
  streamServerIP: string;
  deviceID: string;
  channelId: string;
}

interface ModelData {
  id: string;
  modelName: string;
  modelType: string;
  modelBaseName: string;
  modelBaseUrl: string;
  datasetsId: string;
  datasetsName: string;
  datasetsUrl: string;
  cmdTrain: string;
  configs: string;
  modelAfterTrainingUrl: string;
  nodeId: string;
  logs: string;
  memo: string;
  twfCreated: null;
  twfModified: null;
  twfDeleted: null;
}

interface NodeData {
  id: string,
  saddress: string
  sname: string
}

class MyPoint {
  constructor(public x1: number, public y1: number) {
  }
}

class MyLine {
  constructor(public x1: number, public y1: number, public x2: number | null = null, public y2: number | null = null) {
  }

  LineUpdate(x1: number, y1: number) {
    this.x2 = x1;
    this.y2 = y1;
  }

  LineDraw(ctx: CanvasRenderingContext2D) {
    ctx.lineWidth = 2;
    ctx.strokeStyle = "red";
    ctx.beginPath();
    ctx.moveTo(this.x1, this.y1);
    ctx.lineTo(this.x2!, this.y2!);
    ctx.stroke();
  }
}
interface PushInfo {
  id: string | number;
  sprotoName?: string;
  sname?: string;
  susername?: string;
  spassword?: string;
  stoken?: string;
}
interface NodesOrLinks {
  id: string | number;
  saddress?: string;
  linkName?: string;
  sname?: string;
}

    // 定义一个接口用于线段的数据结构
interface Segment {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}


export interface ImagesInfo {
  id: string | number;
  imgName?: string;
  imgUrl?: string,
  platform?: string,
  type?: string,
  memo?: string,
}
interface Point {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export default defineComponent({
  methods: {mediaListInfo},
  components: {
    Jessibuca,
    jessibucaPlayer,
  },
  setup() {
    const visible = ref(false)
    const dataForm = reactive({
  id: "",
  nodeId: "",
  linkId: "",
  linkName: "",
  configs: ""
});
const nodesInfo = ref<NodesOrLinks[]>([]);
const linksInfo = ref<NodesOrLinks[]>([]);
// const rules = ref({
//   nodeId: [{ required: true, message: $t("validate.required"), trigger: "blur" }],
//   linkId: [{ required: true, message: $t("validate.required"), trigger: "blur" }]
// });
// const initForm = initDataForm(dataForm);
    const imageInfo = ref<ImagesInfo[]>([]); // 训练列表
    const pushInfo = ref<PushInfo[]>([]); // 推送列表
    const {$t} = globalLanguage();
    const view = reactive({
      getDataListURL: "/edge/tcamerainfo/page",
      dataForm: {
        id: "",
        limit: 512
      }
    });
    const state = reactive({...useView(view), ...toRefs(view)});
    const jessibucaRef = ref<InstanceType<typeof jessibuca> | null>(null);
    const videoUrl = ref("");
    const tabActiveName = ref("media");
    const channelId = ref("");
    const streamId = ref("");
    const mediaServerId = ref("");
    const deviceName = ref("")
    const cameraId = ref("")
    const camPos = ref("")
    const streamInfo = ref<StreamInfo | null>(null);
    const videoInfo = ref<string | null>(null);
    const audioInfo = ref<string | null>(null);
    const streamArrInfo = ref<string | null>(null);
    const coordinatesInfo = ref<string | null>(null);
    const hasAudio = ref(false);
    const isChanged = ref(false);
    const isDrawing = ref(false);
    const polygonLineArray = ref<MyLine[]>([]);
    const polygonLineMaxCount = ref(5);
    const polygonLine = ref<MyLine | null>(null);
    const videoCanvas = ref<HTMLCanvasElement | null>(null);
    const videoCanvasCtx = ref<CanvasRenderingContext2D | null>(null);
    const videoCanvasBox = ref()
    const modelInfo = ref<ModelData[]>([]);
    const nodeInfo = ref<NodeData[]>([]);
    const rules = {
      videoStream: [
        {required: true, message: '请选择视频流', trigger: 'change'}
      ],
      trainName: [
        {required: true, message: '请选择训练名称', trigger: 'change'}
      ],
      threshold: [
        {required: true, message: '请输入阈值', trigger: 'blur'},
        {type: 'number', message: '阈值必须为数字', trigger: 'blur'}
      ],
      timeInter: [
        {required: true, message: '请输入时间间隔', trigger: 'blur'},
        {type: 'number', message: '时间间隔必须为数字', trigger: 'blur'}
      ],
      analysisFrame: [
        {required: true, message: '请输入分析帧', trigger: 'blur'},
        {type: 'number', message: '分析帧必须为数字', trigger: 'blur'}
      ],
      roi: [
        {required: true, message: '请绘制布控坐标', trigger: 'blur'}
      ]
    };


// 函数用于提取并返回坐标列表（不包括第一个点）
function extractCornersAsList(segments: Segment[]): number[] {
  // 创建一个空数组来存储平面坐标
  let flatList: number[] = [];

  // 检查是否有线段数据
  if (segments.length === 0) {
    return flatList;
  }

  // 遍历所有线段，只添加终点坐标
  for (const segment of segments) {
    // 将每个线段的终点以x, y形式扩展到列表中
    flatList.push(segment.x2, segment.y2);
  }

  // 如果只有一个点，则返回空数组
  if (flatList.length < 4) {
    return [];
  }

  // 由于起点被重复添加，我们需要移除重复的最后一个点
  // 比较最后一个点与第一个点是否相同
  const n = flatList.length;
  if (flatList[0] === flatList[n - 2] && flatList[1] === flatList[n - 1]) {
    flatList.pop(); // 移除重复的y坐标
    flatList.pop(); // 移除重复的x坐标
  }

  // 返回平面坐标列表
  return flatList;
}

const dataForms = reactive({
  id: "",
  nodeId: "",
  linkId: "",
  linkName: "",
  configs: ""
});
// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/imagesinfo/page"), //加载镜像列表
    baseService.get("/edge/tnodeinfo/page"), // 加载node列表
    baseService.get("/edge/tcamerainfo/page"), //加载摄像头列表
    baseService.get("/edge/tpushinfo/page"), //加载推送列表
    baseService.get("/edge/tlinkinfo/page") // 加载links列表
  ]).then(([ images, nodeInfo ,  tcamerainfo,tpushinfo ,linkInfo ]) => {
    imageInfo.value = images.data.list;
    pushInfo.value = tpushinfo.data.list;
    linksInfo.value = linkInfo.data.list;
  });
};

getAllInfo()

    onMounted(() => {
      videoCanvas.value = document.querySelector(".videoCanvas") as HTMLCanvasElement;
      videoCanvasCtx.value = videoCanvas.value.getContext("2d");
      baseService
        .get('/edge/trainmodel/page')
        .then((res) => {
          console.log(res.data)
          modelInfo.value = [...modelInfo.value, ...res.data.list];
        })
      baseService
        .get('/edge/tnodeinfo/page')
        .then((res) => {
          nodeInfo.value = [...nodeInfo.value, ...res.data.list];
          console.log(nodeInfo.value)
        })

    });

    onUnmounted(() => {
    });

    function convertToString(data: Point[]): string {
      const coordinates: number[] = [];

      data.forEach((point, index) => {
        if (index === 0) {
          coordinates.push(point.x1, point.y1);
        }
        coordinates.push(point.x2, point.y2);
      });

      return JSON.stringify(coordinates);
    }

    const parsedCoordinatesInfo = computed(() => {
      if (coordinatesInfo.value) {
        return JSON.parse(coordinatesInfo.value) as Point[];
      }
      return null;
    });

    function extractFilename(url: string): string {
      // Split the URL by '/'
      const parts = url.split('/');
      // Return the last part which is the filename
      return parts[parts.length - 1];
    }

    // 选择镜像
const onSelectImages = (value: any) => {
  let it = imageInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.imgName) {
    forms.value.modelAfterTrainingUrl = it.imgUrl ?? '';
    const filename = extractFilename(forms.value.modelAfterTrainingUrl);
    forms.value.modelBaseName = filename ?? '';
    // forms.value.trainId = it.id.toString();
    forms.value.trainName = it.imgName ?? '';
  }
};

    // 选择model
    // const onSelectTrain = (value: any) => {
    //   let it = modelInfo.value.find((item) => {
    //     return (item.id = value);
    //   });
    //   if (it?.modelName) {
    //     forms.value.modelAfterTrainingUrl = it.modelAfterTrainingUrl ?? '';
    //     const filename = extractFilename(forms.value.modelAfterTrainingUrl);
    //     forms.value.modelBaseName = filename ?? '';
    //     forms.value.modelAfterTrainingUrl = it.modelAfterTrainingUrl;
    //   }
    // };

    const play = (stream_Info: StreamInfo, has_Audio: boolean) => {
      streamInfo.value = stream_Info;
      hasAudio.value = has_Audio;
      videoUrl.value = stream_Info.streamServerIP + stream_Info.deviceID + stream_Info.channelId + ".live.flv";
      playFromStreamInfo(false, videoUrl.value);
    };

    const playFromStreamInfo = (realHasAudio: boolean, videoUrl: string) => {
      hasAudio.value = realHasAudio && hasAudio.value;
      if (jessibucaRef.value) {
        console.log(jessibucaRef.value);
        jessibucaRef.value.play(videoUrl);
      } else {
        nextTick(() => {
          jessibucaRef.value?.play(videoUrl);
        });
      }
    };

    const close = () => {
      console.log("关闭视频");
      if (jessibucaRef.value) {
        jessibucaRef.value.pause();
      }
      baseService
        .post('/index/api/close_streams',
          {
            secret: app.media_secret,
            force: true,
          }).then((res) => {
        console.log(res.data.count_closed !== 0? "已关闭":"未关闭");
      })
    };
    const onSelectLinks = (value: string | number) => {
       let it = linksInfo.value.find((item) => {
       return item.id === value;
     });
     if (it?.linkName) {
       dataForms.linkName = it.linkName;
  }
};
    const videoError = (e: any) => {
      console.log("播放器错误：" + JSON.stringify(e));
    };

    const getSelectedItem = () => {
      return (state.dataList as any).list.find((item: { streamUrl: string; }) => item.streamUrl === forms.value.selectStreamUrl);
    };

    const setStreamInfo = async (channelId: string) => {
      if (isChanged.value) {
        close();
        jessibucaRef.value?.destroy();
      }
      isChanged.value = true;
      streamInfo.value = {
        streamServerIP: '',
        deviceID: "/live",
        channelId: "/" + channelId,
      };

      deviceName.value = (state.dataList as any).list.find((item: { streamUrl: string; }) => item.streamUrl === forms.value.selectStreamUrl).name;
      cameraId.value = (state.dataList as any).list.find((item: { streamUrl: string; }) => item.streamUrl === forms.value.selectStreamUrl).camId;
      camPos.value = (state.dataList as any).list.find((item: { streamUrl: string; }) => item.streamUrl === forms.value.selectStreamUrl).lat + ',' + (state.dataList as any).list.find((item: { streamUrl: string; }) => item.streamUrl === forms.value.selectStreamUrl).lng;;

      try {
        await mediaServerRegister(forms.value.selectStreamUrl, channelId);
        // 使用循环来等待媒体信息可用
        let mediaInfo = null;
        while (!mediaInfo) {
          try {
            mediaInfo = await mediaListInfo();
          } catch (error) {
            // 如果获取媒体信息失败,等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        const info = mediaInfo.tracks;

        info.forEach((item: { codec_type: any; codec_id_name: string; fps: string; width: string; height: string; sample_rate: string; sample_bit: string; }) => {
          if (!item.codec_type)
            videoInfo.value = item.codec_id_name + '/' + item.fps + 'FPS/' + item.width + '×' + item.height
          if (item.codec_type)
            audioInfo.value = item.codec_id_name + '/' + item.sample_rate + 'Hz/' + item.sample_bit + 'bit'
        });

      } catch (error) {
        console.error("Error processing media info:", error);
      }
      play(streamInfo.value, true);
      streamArrInfo.value = videoUrl.value;
    };

    const handleMouseDown = (event: MouseEvent) => {
      if (polygonLineArray.value.length < polygonLineMaxCount.value) {
        if (polygonLine.value === null) {
          const x = event.offsetX;
          const y = event.offsetY;
          polygonLine.value = new MyLine(x, y);
        }
      }
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (polygonLine.value) {
        const x = event.offsetX;
        const y = event.offsetY;

        reDrawVideoCanvas();
        polygonLine.value.LineUpdate(x, y);
        polygonLine.value.LineDraw(videoCanvasCtx.value!);
      }
    };

    const handleMouseUp = (event: MouseEvent) => {
  if (polygonLine.value) {
    reDrawVideoCanvas();
    let x = event.offsetX; // 使用 offsetX 获取相对于画布的坐标
    let y = event.offsetY; // 使用 offsetY 获取相对于画布的坐标

    if (polygonLineArray.value.length === polygonLineMaxCount.value - 1) {
      const firstLine = polygonLineArray.value[0];
      x = firstLine.x1;
      y = firstLine.y1;
      isDrawing.value = false;
    }

    polygonLine.value.LineUpdate(x, y);
    polygonLine.value.LineDraw(videoCanvasCtx.value!);
    polygonLineArray.value.push(polygonLine.value);

    if (polygonLineArray.value.length === polygonLineMaxCount.value) {
      polygonLine.value = null;

      // 获取画布的实际宽度和高度
      const canvasWidth = videoCanvasCtx.value!.canvas.width;
      const canvasHeight = videoCanvasCtx.value!.canvas.height;

//现在要把下面的坐标转换为相对于摄像头尺寸的比例坐标
      // const cameraWidth = parseInt(videoInfo.value.split('/')[2].split('×')[0]);
      // const cameraHeight = parseInt(videoInfo.value.split('/')[2].split('×')[1]);


      //这里的坐标是相对于画布的坐标，需要转换为相对于摄像头尺寸的比例坐标
      let cameraWidth: number;
      let cameraHeight: number;
      //videoInfo可能为空，需要判断一下
      if (videoInfo.value) {
        cameraWidth = parseInt(videoInfo.value.split('/')[2].split('×')[0]);
        cameraHeight = parseInt(videoInfo.value.split('/')[2].split('×')[1]);
}

      const convertedPolygonLineArray = polygonLineArray.value.map(line => {
        const newLine = new MyLine(
          //这里的cameraWidth和cameraHeight为什么拿不到？
          Math.round((line.x1 / canvasWidth) * cameraWidth),
          Math.round((line.y1 / canvasHeight) * cameraHeight)
        );
        newLine.x2 = Math.round((line.x2! / canvasWidth) * cameraWidth);
        newLine.y2 = Math.round((line.y2! / canvasHeight) * cameraHeight);
        return newLine;
      });

      coordinatesInfo.value = JSON.stringify(convertedPolygonLineArray, null, 4);
      if (parsedCoordinatesInfo.value !== null) {
        forms.value.roi = convertToString(parsedCoordinatesInfo.value);
      }

      // 转换 MyLine 对象为 Segment 对象
      const segments: Segment[] = convertedPolygonLineArray.map(line => ({
        x1: line.x1,
        y1: line.y1,
        x2: line.x2!,
        y2: line.y2!
      }));

      // 提取角的坐标并打印
      const cornersFlatList: number[] = extractCornersAsList(segments);
      console.log("坐标列表:", cornersFlatList);
      forms.value.roi = JSON.stringify(cornersFlatList);
    } else {
      polygonLine.value = new MyLine(x, y);
    }

    console.log("mouseup() polygonLineArray.length =", polygonLineArray.value.length);
    console.log(polygonLineArray.value);
  }
};





    const dataSubmit = () => {
      baseService
        .post('/edge/tdeployinfo/', {
          id: "",
          nodeId: forms.value.nodeId,
          linkId: dataForms.linkId,
          linkName: dataForms.linkName,
          configs: {
            send_url: forms.value.sendUrl,
            video_input: forms.value.selectStreamUrl,
            threshold: forms.value.threshold,
            time_inter: forms.value.timeInter,
            analysis_frame: forms.value.analysisFrame,
            device_id: deviceName.value,
            camera_id: cameraId.value,
            camera_local: "['xzu','91.18577','29.64932']",
            roi: forms.value.roi,
          }
        })
        .then((res) => {
          ElMessage.success({
            message: '成功',
            duration: 500,
          });
          router.push('/edge/tdeployinfo');
        });
    }

    const handleChange = (value: string) => {
      const selectedItem = modelInfo.value.find(item => item.modelBaseName === value);
      if (selectedItem) {
        forms.value.modelName = selectedItem.modelBaseName;
        forms.value.nodeId = selectedItem.nodeId;
        forms.value.modelAfterTrainingUrl = selectedItem.modelAfterTrainingUrl;
      }
    };

    const startDraw = () => {
      isDrawing.value = true;
      adjustVideoCanvas();

      videoCanvas.value!.addEventListener("mousedown", handleMouseDown);
      videoCanvas.value!.addEventListener("mousemove", handleMouseMove);
      videoCanvas.value!.addEventListener("mouseup", handleMouseUp);
    };

    const adjustVideoCanvas = () => {
      const w = videoCanvasBox.value.offsetWidth - 1.8;
      const h = videoCanvasBox.value.offsetHeight - 2.2;
      videoCanvas.value!.setAttribute("width", w.toString());
      videoCanvas.value!.setAttribute("height", h.toString());
      clearVideoCanvas();
    };

    const clearVideoCanvas = () => {
      videoCanvas.value!.removeEventListener("mousedown", handleMouseDown);
      videoCanvas.value!.removeEventListener("mousemove", handleMouseMove);
      videoCanvas.value!.removeEventListener("mouseup", handleMouseUp);
      videoCanvasCtx.value!.clearRect(0, 0, videoCanvas.value!.width, videoCanvas.value!.height);
      polygonLineArray.value = [];
    };

    const reDrawVideoCanvas = () => {
      videoCanvasCtx.value!.clearRect(0, 0, videoCanvas.value!.width, videoCanvas.value!.height);
      polygonLineArray.value.forEach((line) => line.LineDraw(videoCanvasCtx.value!));
    };

    const playStart = () => {
      jessibucaRef.value?.play(videoUrl.value);
    };

    const playStop = () => {
      jessibucaRef.value?.pause();
    };

    const popoverStyles =  {
        backgroundColor: '#f5f5f5',
        width: '25vw'
      }
    const forms = ref<Forms>({
      selectStreamUrl: "",
      modelUrl: "",
      modelName: "",
      sendUrl: "",
      selectModel: "",
      roi: "",
      sname: "",
      threshold: "0.3",
      timeInter: "10",
      analysisFrame: "30",
      trainName: "",
      modelAfterTrainingUrl: "",
      nodeId: "",
      modelBaseName: "",
      outputStream: "",
      nodeName: "",
      containerId: "",
      predictPath: ""
    });
    const onSelectPush = (value: any) => {
  let it = pushInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.sprotoName) {
    forms.value.sendUrl = it.sprotoName || '';
  }
};
    return {
      onSelectImages,
      linksInfo,
      onSelectLinks,
      imageInfo,
      dataForms,
      popoverStyles,
      onSelectPush,
      nodeInfo,
      modelInfo,
      deviceName,
      cameraId,
      forms,
      jessibucaRef,
      videoUrl,
      tabActiveName,
      channelId,
      streamId,
      mediaServerId,
      streamInfo,
      videoInfo,
      audioInfo,
      streamArrInfo,
      coordinatesInfo,
      hasAudio,
      isChanged,
      isDrawing,
      polygonLineArray,
      polygonLineMaxCount,
      polygonLine,
      videoCanvas,
      videoCanvasCtx,
      videoCanvasBox,
      state,
      rules,
      camPos,
      handleChange,
      dataSubmit,
      play,
      playFromStreamInfo,
      close,
      videoError,
      getSelectedItem,
      setStreamInfo,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
      startDraw,
      adjustVideoCanvas,
      clearVideoCanvas,
      reDrawVideoCanvas,
      playStart,
      playStop,
      pushInfo
    };
  },
});
</script>

<style>
#jessibuca:focus {
  outline: -webkit-focus-ring-color auto 0px;
}
.videoCanvas{
  margin: 0 0;
  position: absolute;
  top: 0;
  /*width: 800px;*/
  /*height: 400px;*/
  z-index: 1;
}
.el-button + .el-button {
  margin-left: 8px;
}
</style>
