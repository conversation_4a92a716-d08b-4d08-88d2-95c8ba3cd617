export namespace Dashboard {
    export interface OsInfo {
        os: string;
        platform: string;
        platformFamily: string;
        kernelArch: string;
        kernelVersion: string;

        diskSize: number;
    }
    export interface BaseInfo {
        websiteNumber: number;
        databaseNumber: number;
        cronjobNumber: number;
        appInstalledNumber: number;

        hostId: string;
        hostname: string;
        os: string;
        platform: string;
        platformFamily: string;
        platformVersion: string;
        kernelArch: string;
        kernelVersion: string;
        virtualizationSystem: string;
        ipv4Addr: string;
        systemProxy: string;

        cpuCores: number;
        cpuLogicalCores: number;
        cpuModelName: string;

        currentInfo: CurrentInfo;
    }
    export interface CurrentInfo {
        hostId: string;
        uptime: number;
        timeSinceUptime: string;
        procs: number;

        load1: number;
        load5: number;
        load15: number;
        loadUsagePercent: number;

        cpuPercent: Array<number>;
        cpuUsedPercent: number;
        cpuUsed: number;
        cpuTotal: number;

        memoryTotal: number;
        memoryAvailable: number;
        memoryUsed: number;
        memoryUsedPercent: number;
        swapMemoryTotal: number;
        swapMemoryAvailable: number;
        swapMemoryUsed: number;
        swapMemoryUsedPercent: number;

        ioReadBytes: number;
        ioWriteBytes: number;
        ioCount: number;
        ioReadTime: number;
        ioWriteTime: number;

        diskData: Array<DiskInfo>;

        gpuData: Array<GPUInfo>;
        xpuData: Array<XPUInfo>;

        netBytesSent: number;
        netBytesRecv: number;

        shotTime: number;
    }
    // 磁盘信息
    export interface DiskInfo {
        path: string;
        type: string;
        device: string;
        total: number;
        free: number;
        used: number;
        usedPercent: number;

        inodesTotal: number;
        inodesUsed: number;
        inodesFree: number;
        inodesUsedPercent: number;
    }
    // GPU信息
    export interface GPUInfo {
        index: number;
        productName: string;
        gpuUtil: string;
        temperature: string;
        performanceState: string;
        powerUsage: string;
        memoryUsage: string;
        fanSpeed: string;
    }
    // XPU信息
    export interface XPUInfo {
        deviceID: number;
        deviceName: string;
        memory: string;
        temperature: string;
        memoryUsed: string;
        power: string;
        memoryUtil: string;
    }

    // 请求参数
    export interface DashboardReq {
        scope: string;
        ioOption: string;
        netOption: string;
    }

    // 模型信息
    export interface ModelInfoDto {
      // 算法总数
      algorithmCount: number;
      // 已部署模型数量
      modelCount: number;
      // 运行中模型数量
      runningModelCount: number;
      // 视频流数量
      streamCount: number;
      // 运行中视频流数量
      runningStreamCount: number;
      // 告警数量
      alarmCount: number;
      // 今日告警数量
      todayAlarmCount: number;
  }

}
