<template>
  <div class="mod-edge__cameragrouping">
    <el-form :inline="true" @keyup.enter="getDataTree()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="deleteBatchHandle()" :disabled="selectedIds.length === 0">删除</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="dataListLoading" :data="treeData" row-key="id" border style="width: 100%" @selection-change="selectionChangeHandle" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="gName" label="分组名称" header-align="center" min-width="150"></el-table-column>
      <el-table-column prop="memo" label="备注" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createdAt" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="200">
        <template v-slot="scope">
          <el-button type="primary" link @click="cameraConfigHandle(scope.row)">摄像头配置</el-button>
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="primary" link @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <add-or-update ref="addOrUpdateRef" @refreshDataList="getDataTree">确定</add-or-update>
    <camera-config-dialog v-if="cameraConfigDialogVisible" v-model="cameraConfigDialogVisible" :group-id="currentGroupIdForConfig || ''" :group-name="currentGroupNameForConfig" @saved="getDataTree" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from "vue";
import AddOrUpdate from "./cameragrouping-add-or-update.vue";
import CameraConfigDialog from "./camera-config-dialog.vue";
import { getCameraGroupingTree, safeApiCall } from "@/utils/api";
import type { AxiosResponse } from "axios";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { useRouter } from "vue-router";

interface BackendResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

interface CameraGroupTreeNode {
  id: string;
  gName: string;
  parentId: string | null;
  memo?: string;
  createdAt?: string;
  children?: CameraGroupTreeNode[];
}

interface SafeCallError {
  code: -1;
  data: null;
  msg: string;
}

const router = useRouter();

const treeData = ref<CameraGroupTreeNode[]>([]);
const dataListLoading = ref(false);
const selectedIds = ref<string[]>([]);

const addOrUpdateRef = ref();
const cameraConfigDialogRef = ref();
const cameraConfigDialogVisible = ref(false);
const currentGroupIdForConfig = ref<string | null>(null);
const currentGroupNameForConfig = ref<string>("");

const addOrUpdateHandle = (id?: string) => {
  addOrUpdateRef.value.init(id);
};

const cameraConfigHandle = (row: CameraGroupTreeNode) => {
  // console.log("[Debug] cameraConfigHandle called with row:", JSON.parse(JSON.stringify(row)));
  currentGroupIdForConfig.value = row.id;
  currentGroupNameForConfig.value = row.gName;
  cameraConfigDialogVisible.value = true;
  // console.log("[Debug] cameraConfigDialogVisible set to:", cameraConfigDialogVisible.value);
};

const getIdsToDelete = (nodeId: string, currentTreeData: CameraGroupTreeNode[]): string[] => {
  const ids: string[] = [];
  const findAndCollect = (nodes: CameraGroupTreeNode[], targetId: string, collectMode: boolean) => {
    for (const node of nodes) {
      if (collectMode || node.id === targetId) {
        ids.push(node.id);
        if (node.children && node.children.length > 0) {
          findAndCollect(node.children, targetId, true);
        }
        if (!collectMode && node.id === targetId) {
          return true;
        }
      } else if (node.children && node.children.length > 0) {
        if (findAndCollect(node.children, targetId, false)) {
          return true;
        }
      }
    }
    return false;
  };
  findAndCollect(currentTreeData, nodeId, false);
  return Array.from(new Set(ids));
};

const nodeHasChildren = (nodeId: string, currentTreeData: CameraGroupTreeNode[]): boolean => {
  const findNode = (nodes: CameraGroupTreeNode[], id: string): CameraGroupTreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const foundInChildren = findNode(node.children, id);
        if (foundInChildren) {
          return foundInChildren;
        }
      }
    }
    return null;
  };
  const node = findNode(currentTreeData, nodeId);
  return !!(node && node.children && node.children.length > 0);
};

const getDataTree = async () => {
  dataListLoading.value = true;
  // console.log("[Debug] getDataTree: Starting to fetch tree data.");
  const result = await safeApiCall(getCameraGroupingTree);
  // console.log("[Debug] getDataTree: Result from safeApiCall(getCameraGroupingTree):", JSON.parse(JSON.stringify(result)));

  if (result && (result as SafeCallError).code === -1) {
    ElMessage.error("加载分组树失败 (safeApiCall): " + (result as SafeCallError).msg);
    treeData.value = [];
    console.error("[Debug] getDataTree: safeApiCall reported error.", result);
  } else {
    const axiosResponse = result as AxiosResponse<BackendResponse<CameraGroupTreeNode[]>>;
    // console.log("[Debug] getDataTree: AxiosResponse object:", JSON.parse(JSON.stringify(axiosResponse)));
    if (axiosResponse && axiosResponse.data) {
      const backendData = axiosResponse.data;
      // console.log("[Debug] getDataTree: Backend data from response:", JSON.parse(JSON.stringify(backendData)));
      if (backendData.code === 0 && Array.isArray(backendData.data)) {
        treeData.value = backendData.data;
        // console.log("[Debug] getDataTree: Successfully populated treeData:", JSON.parse(JSON.stringify(treeData.value)));
      } else {
        ElMessage.error("加载分组树失败: " + (backendData.msg || "数据格式错误"));
        treeData.value = [];
        console.error("[Debug] getDataTree: Backend data error or not an array.", backendData);
      }
    } else {
      ElMessage.error("加载分组树失败: 响应无效");
      treeData.value = [];
      console.error("[Debug] getDataTree: Invalid axiosResponse or axiosResponse.data.", axiosResponse);
    }
  }
  dataListLoading.value = false;
};

const selectionChangeHandle = (selection: CameraGroupTreeNode[]) => {
  selectedIds.value = selection.map((item) => item.id);
};

// Delete single item
const deleteHandle = (id: string) => {
  const hasChildren = nodeHasChildren(id, treeData.value);
  const confirmMessage = hasChildren ? "此分组包含子分组，删除操作会一并删除其所有子分组。是否要删除这些分组？" : "确定进行删除操作?";
  const confirmTitle = hasChildren ? "请注意" : "提示";

  ElMessageBox.confirm(confirmMessage, confirmTitle, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      const idsToDeleteArray: string[] = getIdsToDelete(id, treeData.value);
      if (idsToDeleteArray.length === 0) {
        console.warn(`[Debug] deleteHandle: Node with ID ${id} not found for deletion by getIdsToDelete. Falling back to original ID.`);
        idsToDeleteArray.push(id);
      }
      // console.log("[Debug] deleteHandle: IDs (strings) to delete:", idsToDeleteArray);
      // Reverted to use { data: idsToDeleteArray } for proper request body formatting with axios
      baseService
        .delete(`/edge/cameragrouping`, idsToDeleteArray)
        .then((res: BackendResponse) => {
          if (res.code === 0) {
            ElMessage.success("删除成功");
            getDataTree(); // Refresh tree
          } else {
            ElMessage.error(res.msg || "删除失败");
          }
        })
        .catch((err) => {
          console.error("Delete error:", err);
          ElMessage.error("删除请求失败");
        });
    })
    .catch(() => {});
};

// Batch delete
const deleteBatchHandle = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning("请选择要删除的记录");
    return;
  }

  let anySelectedHasChildren = false;
  const allIdsToDeleteSet = new Set<string>();

  for (const id of selectedIds.value) {
    if (nodeHasChildren(id, treeData.value)) {
      anySelectedHasChildren = true;
    }
    const idsForThisNode: string[] = getIdsToDelete(id, treeData.value);
    idsForThisNode.forEach((gid) => allIdsToDeleteSet.add(gid));
  }

  const finalIdsToDelete: string[] = Array.from(allIdsToDeleteSet);

  const confirmMessage = anySelectedHasChildren ? `选中项中包含子分组，删除操作会一并删除其所有子分组。是否要删除这 ${finalIdsToDelete.length} 个实际分组（包含子级）？` : `确定对选中的 ${selectedIds.value.length} 条记录进行批量删除操作?`;
  const confirmTitle = anySelectedHasChildren ? "请注意" : "提示";

  // console.log("[Debug] deleteBatchHandle: Final IDs (strings) to delete:", finalIdsToDelete);
  if (finalIdsToDelete.length === 0) {
    ElMessage.warning("未能确定要删除的分组ID，请重试或联系管理员。");
    return;
  }

  ElMessageBox.confirm(confirmMessage, confirmTitle, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      // Reverted to use { data: finalIdsToDelete } for proper request body formatting with axios
      baseService
        .delete(`/edge/cameragrouping`, finalIdsToDelete)
        .then((res: BackendResponse) => {
          if (res.code === 0) {
            ElMessage.success("批量删除成功");
            getDataTree(); // Refresh tree
            selectedIds.value = []; // Clear selection
          } else {
            ElMessage.error(res.msg || "批量删除失败");
          }
        })
        .catch((err) => {
          console.error("Batch delete error:", err);
          ElMessage.error("批量删除请求失败");
        });
    })
    .catch(() => {});
};

onMounted(() => {
  // console.log("[Debug] cameragrouping.vue: Component onMounted hook executing.");
  getDataTree();
});
</script>
