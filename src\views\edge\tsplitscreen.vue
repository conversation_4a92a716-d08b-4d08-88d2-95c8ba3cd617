<template>
  <div id="devicePosition" style="width: 100%; height: 91vh">
    <el-container style="height: 91vh">
      <el-aside width="200px" style="background-color: #ffffff">
        <el-row>
          <h4 style="margin-left: 10px">摄像头原始视频流</h4>
        </el-row>
        <el-scrollbar max-height="400px" style="height: 400px">
          <el-input v-model="SearchInput" placeholder="请输入设备ID" @keyup.enter="getCameras" clearable size="small" style="width: 100px;margin-left: 10px;"></el-input>
          <el-button type="primary" plain @click="getCameras" size="small" style="margin-left: 10px">查询</el-button>
          <el-tree ref="tree_cam" :data="treeData_cam" :key="treeData_cam" node-key="id" @node-click="clickEvent(true)" :props="defaultProps" check-on-click-node style="height: 40%"></el-tree>
        </el-scrollbar>
        <!-- <el-divider></el-divider> -->
        <!-- <el-row>
          <h4 style="margin-left: 10px">算法处理后视频流</h4>
        </el-row>
        <el-scrollbar max-height="400px" style="height: 400px">
          <el-tree
            ref="tree_train"
            :data="treeData_train"
            :key="treeData_train"
            node-key="id"
            @node-click="clickEvent(false)"
            :props="defaultProps"
            check-on-click-node
            style="height: 40%"
          ></el-tree>
        </el-scrollbar> -->
      </el-aside>
      <el-container>
        <el-header height="5vh" style="text-align: left; font-size: 17px; line-height: 5vh; display: flex; align-items: flex-start">
          <h4 style="display: inline-block; vertical-align: middle; margin: 0">分屏:</h4>
          <div style="margin-left: 2px; display: inline-block; vertical-align: middle">
            <el-icon :class="{ active: split === 9 }" @click="split = 9" style="font-size: 24px; margin-left: 2px; display: inline-block; vertical-align: middle">
              <Grid />
            </el-icon>
          </div>
          <div style="margin-left: 2px; display: inline-block; vertical-align: middle">
            <el-icon :class="{ active: split === 4 }" @click="split = 4" style="font-size: 24px; margin-left: 2px; display: inline-block; vertical-align: middle">
              <Menu />
            </el-icon>
          </div>
          <div style="margin-left: 2px; display: inline-block; vertical-align: middle">
            <el-icon :class="{ active: split === 1 }" @click="split = 1" style="font-size: 24px; margin-left: 2px; display: inline-block; vertical-align: middle">
              <Platform />
            </el-icon>
          </div>
          <div style="margin-left: 2px; display: inline-block; vertical-align: middle">
            <el-button @click="randomizeScreens" type="primary" style="font-size: 14px; margin-left: 2px; display: inline-block; vertical-align: middle"> 随机播放 </el-button>
          </div>
        </el-header>
        <el-main style="padding: 0">
          <div style="width: 99%; height: 85vh; display: flex; flex-wrap: wrap; background-color: #000">
            <div v-for="i in split" :key="i" class="play-box" :style="{ ...liveStyle }" :class="{ redborder: playerIdx === i - 1 }" @click="playerIdx = i - 1">
              <div v-if="!videoUrl[i - 1]" style="color: #cccccc; font-size: 30px; font-weight: bold">{{ i }}</div>
              <player :ref="(el) => (playerRefs[i - 1] = el)" style="background-color: #f1f2f4;" v-else :videoUrl="videoUrl[i - 1]" fluent autoplay @destroy="destroy" />
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, toRefs, watch } from "vue";
import Player from "@/views/device/jessibuca.vue";
import { mediaServerRegister } from "@/utils/media";
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import logo from "@/assets/images/logo.png";
const SearchInput = ref<any>();
interface TreeNode {
  id: string;
  label: string;
  streamUrl: string | null;
  isLeaf: boolean;
  children?: TreeNode[];
}

const view = reactive({
  getDataListURL: "/edge/tcamerainfo/page",
  dataForm: {
    id: "",
    limit: 512
  }
});

interface TrainInfo {
  id: string;
  train_id: string;
  train_name: string;
  model_after_training_url: string;
  node_id: string;
  node_name: string;
  model: string;
  send_url: string;
  video_input: string;
  output_stream: string;
  threshold: string;
  time_inter: string;
  analysis_frame: string;
  device_id: string;
  camera_id: string;
  camera_local: string;
  roi: string;
  container_id: string;
  predict_path: string;
  deploy_status: string;
  twf_created: string;
  twf_modified: string;
  twf_deleted: string;
}
const state = reactive({ ...useView(view), ...toRefs(view) });
const videoUrl = ref<string[]>([""]);
const split = ref<number>(9);
const playerIdx = ref<number>(0);
const updateLooper = ref<number>(0);
const tree_cam = ref();
const tree_train = ref();
const total = ref<number>(0);
const treeData_cam = ref<TreeNode[]>([]);
// const treeData_train = ref<TreeNode[]>([]);
const trainInfo = ref<TrainInfo[]>([]);
const defaultProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf"
};
const playerRefs = ref<any[]>([]);

const liveStyle = computed(() => {
  let style = { width: "100%", height: "100%" };
  switch (split.value) {
    case 4:
      style = { width: "50%", height: "50%" };
      break;
    case 9:
      style = { width: "33.33%", height: "33.33%" };
      break;
  }
  nextTick(() => {
    for (let i = 0; i < split.value; i++) {
      const playerRef = playerRefs.value[i];
      playerRef && playerRef.updatePlayerDomSize();
    }
  });
  return style;
});

watch(split, (newValue) => {
  console.log("切换画幅;" + newValue);
  for (let i = 1; i <= newValue; i++) {
    const playerRef = playerRefs.value[i - 1];
    if (!playerRef) {
      continue;
    }
    nextTick(() => {
      if (Array.isArray(playerRef)) {
        playerRef[0].resize();
      } else {
        playerRef.resize();
      }
    });
  }
  window.localStorage.setItem("split", newValue.toString());
});
const getCameras = () => {
  baseService.get('/edge/tcamerainfo/page', { name: SearchInput.value, limit: 512 }).then((res) => {
    state.dataList = res.data;
  });
};
baseService.get("/edge/predictdeploy/page").then((res) => {
  trainInfo.value = [...trainInfo.value, ...res.data.list];
  console.log(trainInfo.value);
});

watch(
  () => state.dataList,
  (newVal) => {
    console.log(newVal);

    if (newVal) {
      const parentNodeMap: { [key: string]: TreeNode } = {};

      (newVal as any).list.forEach((item: { name: any; id: any; ipAddr: any; streamUrl: any }) => {
        const name = item.name;
        if (!parentNodeMap[name]) {
          parentNodeMap[name] = {
            id: name,
            label: name,
            streamUrl: null,
            isLeaf: false,
            children: []
          };
        }

        parentNodeMap[name].children!.push({
          id: item.id,
          label: item.streamUrl,
          streamUrl: item.streamUrl,
          isLeaf: true
        });
      });

      // 将两个根节点作为 treeData 的顶层节点
      treeData_cam.value = Object.values(parentNodeMap);
      console.log(treeData_cam.value);
    }
  }
);

watch(
  () => trainInfo.value,
  (newVal) => {
    // 处理 trainInfo 数据并加入到 rootNode2 中
    const parentNodeMap: { [key: string]: TreeNode } = {};

    (newVal as any).forEach((item: { trainName: any; id: any; outputStream: any }) => {
      const trainName = item.trainName;
      if (!parentNodeMap[trainName]) {
        parentNodeMap[trainName] = {
          id: trainName,
          label: trainName,
          streamUrl: null,
          isLeaf: false,
          children: []
        };
      }

      parentNodeMap[trainName].children!.push({
        id: item.id,
        label: item.outputStream,
        streamUrl: item.outputStream,
        isLeaf: true
      });
    });

    // 将两个根节点作为 treeData 的顶层节点
    // treeData_train.value = Object.values(parentNodeMap);
    // console.log(treeData_train.value)
  }
);

onMounted(() => {});

onBeforeUnmount(() => {
  clearTimeout(updateLooper.value);
});

const destroy = (idx: string) => {
  console.log(idx);
  clear(idx.substring(idx.length - 1));
};

const clickEvent = (n: boolean) => {
  const tree = n ? tree_cam.value : tree_train.value;
  console.log((tree as any).getCurrentNode());
  if ((tree as any).getCurrentNode().isLeaf) {
    sendDevicePush(tree.getCurrentNode());
  }
};

const sendDevicePush = (itemData?: any) => {
  const deviceId = "live";
  const channelId = itemData.id;
  const idxTmp = playerIdx.value;
  const streamUrl = itemData.streamUrl;
  save(itemData);
  mediaServerRegister(streamUrl, channelId);
  console.log("通知设备推流1：" + deviceId + " : " + channelId);
  setPlayUrl("" + "/live/" + channelId + ".live.flv", idxTmp);
};

const setPlayUrl = (url: string, idx: number) => {
  videoUrl.value[idx] = url;
  nextTick(() => {
    const playerRef = playerRefs.value[idx];
    if (playerRef) {
      playerRef.load(url); // Assuming the player component has a load method to set the URL
    } else {
      console.error(`Player reference not found for index: ${idx}`);
    }
  });
};

const save = (item: TreeNode) => {
  let dataStr = window.localStorage.getItem("playData") || "[]";
  let data = JSON.parse(dataStr);
  data[playerIdx.value] = item;
  window.localStorage.setItem("playData", JSON.stringify(data));
};

const clear = (idx: string) => {
  let dataStr = window.localStorage.getItem("playData") || "[]";
  let data = JSON.parse(dataStr);
  data[parseInt(idx) - 1] = null;
  console.log(data);
  window.localStorage.setItem("playData", JSON.stringify(data));
};
const randomizeScreens = () => {
  //分屏数量
  split.value = 9;
  const allNodes: any[] = [];
  //获取所有节点
  treeData_cam.value.forEach((parent) => {
    parent.children?.forEach((child) => {
      allNodes.push(child);
    });
  });
  //打乱顺序
  for (let i = allNodes.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [allNodes[i], allNodes[j]] = [allNodes[j], allNodes[i]];
  }
  //实现播放
  for (let i = 0; i < 9; i++) {
    playerIdx.value = i;
    sendDevicePush(allNodes[i]);
  }
};
</script>
<style>
.redborder {
  border: 2px solid red !important;
}

.play-box {
  background-color: #f1f2f4;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-position: center 50%;
  background-size: 24% 40%;
}

.active {
  color: red;
}
</style>
