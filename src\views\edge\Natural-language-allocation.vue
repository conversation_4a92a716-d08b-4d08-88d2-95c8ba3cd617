<template>
  <!-- 节点管理 -->
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="form" @keyup.enter="handleSearch">
      <el-form-item>
        <!-- <el-button :icon="Search" @click="handleSearch">通过编号搜索</el-button> -->
      </el-form-item>
      <el-form-item v-if="hasPermission('sys:user:save')">
        <el-button color="rgba(50,122,230,1)" @click="handleAdd">{{ "新增" }}</el-button>
      </el-form-item>
      <el-form-item v-if="hasPermission('sys:user:delete')">
        <el-button type="danger" @click="deleteHandle()">{{ "删除" }}</el-button>
      </el-form-item>
      <!-- <el-form-item v-if="hasPermission('sys:user:export')">
        <el-button type="info" @click="handleExport">{{ '导出' }}</el-button>
      </el-form-item> -->
    </el-form>

    <el-table v-loading="loading" style="width: 100%; z-index: 1" :data="getAll" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50" class="myRedCheckBox"></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="trainId" label="算法ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="trainName" label="算法名" header-align="center" align="center" width="150px"></el-table-column>
      <el-table-column prop="nodeId" label="部署节点ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="nodeIp" label="部署节点IP" header-align="center" align="center"></el-table-column>
      <el-table-column prop="alUrl" label="URL" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="securityLevel" label="算法" #default="row" header-align="center" align="center">{{ scope.row.securityLevel }}</el-table-column> -->
      <el-table-column label="算法" header-align="center" align="center" :show-overflow-tooltip="true">
        <template #default="scope">
          {{ getName(scope.row.securityLevel) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" header-align="center" align="center">
        <template #default="scope">
          <el-button type="warning" link @click="handleEdit(scope.row.id)">编辑</el-button>
          <el-button type="danger" link @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>

    <el-dialog v-model="dialogVisible" title="新增" width="50%" :before-close="handleClose">
      <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle(), clear()" label-width="120px">
        <el-form-item label="镜像名" prop="trainName">
          <el-select class="m-2" v-model="dataForm.trainName" value-key="id" @change="onSelectImages" placeholder="选择镜像">
            <el-option v-for="item in imageInfo" :key="item.id" :label="item.imgName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="算法ID" prop="trainId">
          <el-input v-model="dataForm.trainId" placeholder="算法ID" disabled></el-input>
        </el-form-item>

        <el-form-item label="算法名" prop="trainName">
          <el-input v-model="dataForm.trainName" placeholder="算法名" disabled></el-input>
        </el-form-item>
        <el-form-item label="部署节点ID" prop="nodeId">
          <!--        <el-input v-model="dataForm.nodeId" placeholder="节点ID"></el-input>-->
          <el-select class="m-2" v-model="dataForm.nodeId" @change="onSelectnodeID" value-key="id" :placeholder="$t('edge.selectModel')" size="large">
            <el-option v-for="item in nodesInfo" :key="item.hostId" :label="item.hostname" :value="item.hostId" />
          </el-select>
        </el-form-item>

        <el-form-item label="部署节点ID" prop="nodeId">
          <el-input v-model="dataForm.nodeId" placeholder="部署节点ID" disabled></el-input>
        </el-form-item>

        <!-- <el-form-item label="部署节点IP" prop="nodeIp">

          <el-select class="m-2" v-model="dataForm.nodeIp" value-key="id" placeholder="选择部署IP" size="large">
            <el-option v-for="item in nodesInfo" :key="item.id" :label="item.sname" :value="item.sv4Address"/>
          </el-select>
        </el-form-item> -->

        <el-form-item label="部署节点IP" prop="nodeIp">
          <el-input v-model="dataForm.nodeIp" placeholder="部署节点IP" disabled></el-input>
        </el-form-item>

        <el-form-item label="URL" prop="alUrl">
          <el-input v-model="dataForm.alUrl" placeholder="URL"></el-input>
        </el-form-item>

        <el-form-item label="算法" prop="securityLevel">
          <el-select class="m-2" v-model="dataForm.securityLevel" @change="changes" value-key="id" placeholder="请选择算法" multiple>
            <el-option v-for="item in alarmTypeInfo" :key="item.id" :label="item.describeInfo" :value="item.securityLevel" />
          </el-select>
          <el-checkbox v-model="selectAll" @change="toggleSelectAll" style="margin-left: 10px">全选</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="(dialogVisible = false), clear()">取消</el-button>
          <el-button type="primary" @click="(dialogVisible = false), dataFormSubmitHandle(getId)"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { onActivated, reactive, ref, toRefs } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { ElMessage, valueEquals } from "element-plus";

const loading = ref(false);
const form = ref({ id: "" });
import { ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
import { get } from "http";
import { watch } from "fs";
import { parse } from "path";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/predictdeploy/page",
  getDataListIsPage: true,
  exportURL: "/edge/predictdeploy/export",
  deleteURL: "/edge/predictdeploy"
});
const selectAll = ref(false);
const toggleSelectAll = () => {
  if (selectAll.value) {
    const arr = ref<any>([]);
    alarmTypeInfo.value.forEach((item) => {
      arr.value.push(item.securityLevel);
    });
    dataForm.securityLevel = arr.value;
  } else {
    dataForm.securityLevel = "";
  }
};
const option = reactive([
  {
    id: 39,
    value: "/entity_recognize",
    label: "实体识别"
  },
  {
    id: 35,
    value: "/get_keyphrase",
    label: "关键词提取"
  },
  {
    id: 37,
    value: "/part_of_speech",
    label: "词性标注"
  },
  {
    id: 38,
    value: "/sentiment_analysis",
    label: "情感分析"
  },
  {
    id: 41,
    value: "/summary_generation",
    label: "文本摘要"
  },
  {
    id: 40,
    value: "/text_classification",
    label: "文本分类"
  },
  {
    id: 36,
    value: "/text_similarity",
    label: "文本相似性分析"
  },
  {
    id: 34,
    value: "/word_segmentation",
    label: "中文分词"
  }
]);
const getName = (row: any) => {
  let name = "";
  console.log(row, 1);
  let rows = row.split(",");
  console.log(option, 2);
  for (let i = 0; i < rows.length; i++) {
    option.forEach((item) => {
      if (item.id.toString() == rows[i]) {
        //如果是最后一个就不加逗号
        if (i == rows.length - 1) {
          name += item.label;
        } else {
          name += item.label + ",";
        }
      }
    });
  }
  return name;
};
const getAll = ref("");
const dialogVisible = ref(false);
const options = ref<{ id: string; describeInfo: string }[]>([]);
const nodesInfo = ref<DashboardSnapshot[]>([]); // 节点
const alarmTypeInfo = ref<AlarmTypeInfo[]>([]); // 推送列表
const imageInfo = ref<ImagesInfo[]>([]); // 训练列表
const cameraInfo = ref<Camerainfo[]>([]); // 摄像头列表
const pushInfo = ref<PushInfo[]>([]); // 推送列表

const dataForm = reactive({
  id: "",
  trainId: "",
  trainName: "",
  alUrl: "",
  nodeId: "",
  nodeIp: "",
  securityLevel: "",
  modelAfterTrainingUrl: ""
});
const changes = (value: string) => {
  for (let i = 0; i < option.length; i++) {
    if (option[i].id.toString() == value) {
      // dataForm.trainName = option[i].label;
    }
  }
};

export interface Camerainfo {
  id: string;
  uuid?: string;
  name?: string;
  streamUrl?: string;
  camId?: string;
}

export interface PushInfo {
  id: string | number;
  sprotoName?: string;
  sname?: string;
  susername?: string;
  spassword?: string;
  stoken?: string;
}

interface ImagesInfo {
  id: string | number;
  imgName?: string;
  imgUrl?: string;
  platform?: string;
  type?: string;
  memo?: string;
}

// interface NodesOrLinks {
//   id: string | number;
//   saddress?: string;
//   linkName?: string;
//   sname?: string;
//   sv4Address?: string;
// }

interface DashboardSnapshot {

  /** 主机名 */
  hostname: string;

  /** 主机 UUID */
  hostId: string;

  /** 操作系统类型，例如 linux / windows */
  os: string;

  /** 发行版名称，例如 ubuntu */
  platform: string;

  /** 发行版家族，例如 debian */
  platformFamily: string;

  /** 发行版版本号，例如 20.04 */
  platformVersion: string;

  /** 内核架构，例如 x86_64 */
  kernelArch: string;

  /** 内核版本 */
  kernelVersion: string;

  /** IPv4 地址 */
  ipv4Addr: string;

  /** 系统代理配置 */
  systemProxy: string;

  /** 物理 CPU 核心数 */
  cpuCores: number;

  /** 逻辑 CPU 核心数（含超线程） */
  cpuLogicalCores: number;

  /** CPU 型号 */
  cpuModelName: string;

  /** 系统累计运行时长（秒） */
  uptime: string;

  /** 最近一次启动时间（YYYY-MM-DD HH:mm:ss） */
  timeSinceUptime: string;

  /** 当前进程数量 */
  procs: number;

  /** 1 分钟平均负载 */
  load1: number;

  /** 5 分钟平均负载 */
  load5: number;

  /** 15 分钟平均负载 */
  load15: number;

  /** 负载占用率 (%) */
  loadUsagePercent: number;

  /** CPU 使用率 (%) */
  cpuUsedPercent: number;

  /** CPU 已用核心数 */
  cpuUsed: number;

  /** CPU 总核心数（可用） */
  cpuTotal: number;

  /** 内存总量（字节） */
  memoryTotal: string;

  /** 可用内存（字节） */
  memoryAvailable: string;

  /** 已用内存（字节） */
  memoryUsed: string;

  /** 内存使用率 (%) */
  memoryUsedPercent: number;

}

interface AlarmTypeInfo {
  id: string | number;
  securityLevel?: string;
  describeInfo?: string;
}

interface IObject<T = any> {
  [key: string]: T;
}

const getId = ref<number>();
const state = reactive({ ...useView(view), ...toRefs(view) });
const checkList = ref([""]);
const handleClose = (done: () => void) => {
  done();
  clear();
};

const currentPage = ref(1);
const pageSize = ref(10);
const handleSelect = (id: string, describeInfo: string) => {
  console.log(id, describeInfo);
  console.log(checkList.value); //获取选中的值
};
const value = ref<string>(""); // 定义 value，并指定类型为 string
// 模拟权限判断函数
const hasPermission = (obj: string) => {
  const permissions = ["sys:user:save", "sys:user:delete", "sys:user:export"];
  return permissions.includes(obj);
};
const clear = () => {
  // 清空搜索条件
  getId.value = undefined;
  dataForm.id = "";
  (dataForm.trainId = ""), (dataForm.trainName = ""), (dataForm.alUrl = ""), (dataForm.nodeId = ""), (dataForm.nodeIp = ""), (dataForm.securityLevel = "");
};
const total = ref<number>(0);

const handleSearch = () => {
  baseService.get("edge/predictdeploy/getAll").then((res) => {
    getAll.value = res.data;
  });
  baseService
    .get("edge/tmodelcategory/allByType", {
      type: "NLP"
    })
    .then((res) => {
      options.value = res.data.list;
      console.log(res.data.list);
    });
  baseService
    .get("/edge/tmodelcategory/allByType", { type: "NLP" }) //加载预警列表
    .then((res) => {
      alarmTypeInfo.value = res.data.list;
    });
  // baseService.get("/edge/tnodeinfo/page").
  baseService.get("/edge/dashboardsnapshot/page").then((nodeInfo) => {
    nodesInfo.value = nodeInfo.data.list;
  });
  Promise.all([
    baseService.get("/edge/imagesinfo/page", { id: 27 }), //加载镜像列表
    // baseService.get("/edge/tnodeinfo/page"), // 加载node列表
    baseService.get("/edge/dashboardsnapshot/page"), // 加载node列表
    baseService.get("/edge/tcamerainfo/AllInfoList"), //加载摄像头列表
    baseService.get("/edge/tpushinfo/page") //加载推送列表
  ]).then(([images, nodeInfo, tcamerainfo, tpushinfo]) => {
    imageInfo.value = images.data.list;
    // console.log(imageInfo.value);

    nodesInfo.value = nodeInfo.data.list;
    cameraInfo.value = tcamerainfo.data;
    pushInfo.value = tpushinfo.data.list;
  });
};
// 选择镜像
const onSelectImages = (value: any) => {
  let it = imageInfo.value.find((item) => {
    return item.id == value;
  });
  if (it?.imgName) {
    dataForm.trainId = it.id.toString();
    dataForm.trainName = it.imgName ?? "";
    dataForm.modelAfterTrainingUrl = it.imgUrl ?? "";
  }
};

// 选择节点
const onSelectnodeID = (value: any) => {
  let it = nodesInfo.value.find((item) => {
    return item.hostId == value;
  });
  if (it?.hostname) {
    dataForm.nodeId = it.hostId ?? "";
    dataForm.nodeIp = it.ipv4Addr ?? "";
  }
};
const dataFormSubmitHandle = (id?: number) => {
  // 表单提交
  console.log(id);

  //把dataForm.securityLevel转成字符串
  dataForm.securityLevel = dataForm.securityLevel.toString();

  if (id === undefined) {
    baseService.post("edge/predictdeploy/add", dataForm).then((res) => {
      if (res.code === 0) {
        handleSearch();
        ElMessage({
          message: "新增成功",
          type: "success"
        });
      }
    });
    clear();
  } else {
    console.log(dataForm, 1);
    baseService.put("edge/predictdeploy/edit", dataForm).then((res) => {
      if (res.code === 0) {
        handleSearch();
        ElMessage({
          message: "编辑成功",
          type: "success"
        });
      }
    });
    clear();
  }
};
const deleteHandle = (id?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (state.deleteIsBatch && !id && state.dataListSelections && state.dataListSelections.length <= 0) {
      ElMessage.warning({
        message: "请选择操作项",
        duration: 500
      });
      return;
    }
    ElMessageBox.confirm("确定进行[删除]操作?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        baseService.delete(`${state.deleteURL}${state.deleteIsBatch ? "" : "/" + id}`, state.deleteIsBatch ? (id ? [id] : state.dataListSelections ? state.dataListSelections.map((item: IObject) => state.deleteIsBatchKey && item[state.deleteIsBatchKey]) : {}) : {}).then((res) => {
          ElMessage.success({
            message: "成功",
            duration: 500,
            onClose: () => {
              handleSearch();
              resolve(true);
            }
          });
        });
      })
      .catch(() => {
        //
      });
  });
};
const handleAdd = (id?: number) => {
  // 打开新增窗口
  dialogVisible.value = true;
};

const handleEdit = (id: number) => {
  dialogVisible.value = true;
  // 打开编辑窗口
  baseService.get(`edge/predictdeploy/${id}`, { id }).then((res) => {
    if (res.code === 0) {
      (dataForm.id = res.data.id), (dataForm.trainId = res.data.trainId), (dataForm.trainName = res.data.trainName), (dataForm.alUrl = res.data.alUrl), (dataForm.nodeId = res.data.nodeId), (dataForm.nodeIp = res.data.nodeIp);
      // dataForm.securityLevel = res.data.securityLevel.split(',')
    }
  });
  getId.value = id;
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  handleSearch();
};

const handleCurrentChange = (current: number) => {
  currentPage.value = current;
  handleSearch();
};

const rules = reactive({
  trainId: [{ required: true, message: "请输入算法ID", trigger: "blur" }],
  trainName: [{ required: true, message: "请输入算法名", trigger: "blur" }],
  nodeId: [{ required: true, message: "请输入部署节点ID", trigger: "blur" }],
  nodeIp: [{ required: true, message: "请输入部署节点IP", trigger: "blur" }],
  alUrl: [{ required: true, message: "请输入URL", trigger: "blur" }]
});

handleSearch();
</script>

<style scoped>
/* 设置checkbox获得焦点后，对勾框的边框颜色 */
.el-checkbox__inner {
  background-color: #475665;
  border: none;
}

.el-checkbox__label {
  color: red;
}

.is-checked::after {
  background-color: red;
}

.is-focus::after {
  background-color: red;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: red;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: red;
}

.example-showcase .el-dropdown + .el-dropdown {
  margin-left: 15px;
}

.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
