<template>
  <div v-loading="loading">
    <iframe class="iframe" :src="href" @load="load"></iframe>
  </div>
</template>
<script lang="ts" setup>
import baseService from '@/service/baseService';
import { ref } from 'vue';
const loading = ref(true);
const href = ref("");
baseService.get("/sys/params/page",{paramCode: "gpu"}).then(res => {
  href.value = res.data.list[0].paramValue;
})
const load = () => {
  loading.value = false;
};
</script>
<style lang="less" scoped>
.iframe {
  min-height: calc(100vh - 130px);
  width: 100%;
  border: 0;
}
</style>