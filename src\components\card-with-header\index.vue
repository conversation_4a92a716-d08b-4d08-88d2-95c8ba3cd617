<template>
    <div>
        <el-card :style="{ height: height }" class="home-card">
            <div class="header">
                <div class="header-left">
                    <span class="header-title">{{ header }}</span>
                </div>
                <div class="header-right">
                    <slot name="header-r" />
                </div>
            </div>

            <div class="body-content">
                <slot name="body" />
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'CardWithHeader' });
defineProps({
    header: String,
    height: String,
});
</script>

<style scoped>
/* Apply standard card look and feel */
.home-card {
    border-radius: 4px;
    border: 1px solid var(--el-card-border-color, var(--el-border-color-light)); /* Use El Card border or fallback */
    background-color: var(--el-bg-color);
    overflow: hidden; /* Ensure pseudo-elements don't overflow */
    margin-bottom: 20px; /* Add spacing between cards */
}

/* Override default ElCard padding to use custom layout */
:deep(.home-card .el-card__body) {
    padding: 20px; /* Restore standard body padding */
}

.home-card .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px; /* Space between header and body */
}

.home-card .header .header-left {
    /* Takes up available space, pushing right content */
    /* flex-grow: 1; <-- Optional, might not be needed if right pushes itself */
}

.home-card .header .header-title {
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    padding-left: 13px;
}

.home-card .header .header-title::before {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    width: 4px;
    height: 14px;
    content: '';
    background: var(--el-color-primary);
    border-radius: 10px;
}

.home-card .header .header-right {
    /* Pushes this div to the far right */
    margin-left: auto;
    /* Ensure items within the slot align if needed */
    display: flex;
    align-items: center;
}

/* Body content - remove default top margin */
.home-card .body-content {
    margin-top: 0;
}

/* Remove the previous ::v-deep selector for the right slot */
/* .home-card .header ::v-deep(> *:last-child) {
    margin-left: auto;
} */
</style>
