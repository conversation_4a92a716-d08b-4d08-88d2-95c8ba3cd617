<template>
  <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()">

    <div v-if="selectedStage === undefined">
      <el-text class="mx-1" type="info">请在左侧流程图选择节点</el-text>
    </div>

    <div v-if="selectedStage === 'baseInfo'">
      <h3>训练基础信息</h3>
      <el-form-item prop="modelName">
        <div>模型训练名称</div>
        <el-input v-model="dataForm.modelName" placeholder="模型名称"></el-input>
      </el-form-item>
      <el-form-item prop="memo">
        <div>备注</div>
        <el-input v-model="dataForm.memo" placeholder="备注"></el-input>
      </el-form-item>
    </div>

    <div v-if="selectedStage === 'baseConfig'">
      <h3>基础模型配置</h3>
      <el-form-item prop="modelType">
        <div>模型类型 (yolov8,yolov9)</div>
        <el-input v-model="dataForm.modelType" placeholder="模型类型 1: yolov8 2: yolov9 2: padda"></el-input>
      </el-form-item>
      <el-form-item prop="modelBaseName">
        <div>基础模型名称</div>
        <el-select class="m-2" v-model="dataForm.modelBaseName" value-key="id" @change="onSelectModel"
                   style="width: 100%">
          <el-option v-for="item in modelInfo" :key="item.id" :label="item.modelName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item prop="modelBaseUrl">
        <div>基础模型url</div>
        <el-input v-model="dataForm.modelBaseUrl" placeholder="基础模型url 地址"></el-input>
      </el-form-item>
    </div>

    <div v-if="selectedStage === 'dataSet'">
      <h3>数据集配置</h3>
      <el-form-item prop="datasetsName">
        <div>数据集名称</div>
        <!--        <el-input v-model="dataForm.datasetsName" placeholder="数据集名称"></el-input>-->
        <el-select class="m-2" v-model="dataForm.datasetsName" value-key="id" @change="onSelectData"
                   style="width: 100%">
          <el-option v-for="item in dataInfo" :key="item.id" :label="item.datasetsName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item prop="datasetsUrl">
        <div>数据集URL</div>
        <el-input v-model="dataForm.datasetsUrl" placeholder="数据集URL"></el-input>
      </el-form-item>
    </div>

    <div v-if="selectedStage === 'trainConfig'">
      <h3>训练配置</h3>
      <el-form-item prop="cmdTrain">
        <div>执行命令行</div>
        <el-input v-model="dataForm.cmdTrain" placeholder="执行命令行"></el-input>
      </el-form-item>
      <el-form-item prop="configs">
        <div>参数配置</div>
        <el-input v-model="dataForm.configs" placeholder="参数配置"></el-input>
      </el-form-item>
    </div>

    <div v-if="selectedStage === 'exportConfig'">
      <h3>导出配置</h3>
      <el-form-item prop="configs">
        <div>训练后的模型</div>
        <el-input v-model="dataForm.modelAfterTrainingUrl" placeholder="参数配置"></el-input>
      </el-form-item>
      <el-form-item prop="nodeId">
        <div>节点ID</div>
        <el-select class="m-2" v-model="dataForm.nodeId" value-key="id" :placeholder="$t('edge.selectModel')"
                   size="large" style="width: 100%">
          <el-option v-for="item in nodesInfo" :key="item.id" :label="item.saddress" :value="item.saddress"/>
        </el-select>
      </el-form-item>
      <el-button type="primary" @click="dataFormSubmitHandle()">保存配置</el-button>
    </div>

  </el-form>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";
import {DatasetInfo, NodesOrLinks, TrainModelInfo} from "@/types/interface";


const emit = defineEmits(["refreshDataList"]);
const props = defineProps({
  selectedStage: String
});

const dataFormRef = ref();

const dataForm = reactive({
  id: '',
  modelName: '',
  modelType: '',
  modelBaseName: '',
  modelBaseUrl: '',
  datasetsId: '',
  datasetsName: '',
  datasetsUrl: '',
  cmdTrain: '',
  nodeId: '',
  configs: '',
  modelAfterTrainingUrl: '',
  memo: '',
});

const rules = ref({
  modelName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelType: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelBaseName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelBaseUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
  // cmdTrain: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // configs: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // memo: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfCreated: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfModified: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfDeleted: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ]
});

const dataInfo = ref<DatasetInfo[]>([]); // 数据集列表
const modelInfo = ref<TrainModelInfo[]>([]); // 模型列表
const nodesInfo = ref<NodesOrLinks[]>([]);

const init = (id?: number) => {
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  getAllInfo();

  if (id) {
    getInfo(id);
  }else {
    dataForm.id = '';
    dataForm.modelName = ''
    dataForm.modelType = ''
    dataForm.modelBaseName = ''
    dataForm.modelBaseUrl = ''
    dataForm.datasetsId = ''
    dataForm.datasetsName = ''
    dataForm.datasetsUrl = ''
    dataForm.cmdTrain = ''
    dataForm.nodeId = ''
    dataForm.configs = ''
    dataForm.modelAfterTrainingUrl = ''
    dataForm.memo = ''
  }
};

// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/datasetsinfo/page"), //加载摄像头列表
    baseService.get("/edge/trainmodelinfo/page"), //加载模型列表
    baseService.get("/edge/tnodeinfo/page"), // 加载node列表
  ]).then(([datainfo, modelinfo, nodeInfo]) => {
    dataInfo.value = datainfo.data.list;
    modelInfo.value = modelinfo.data.list;
    nodesInfo.value = nodeInfo.data.list;
  });
};

// 选择model
const onSelectModel = (value: any) => {
  // console.log(value);
  let it = modelInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.modelName) {
    dataForm.modelBaseName = it.modelFileName ?? '';
    dataForm.modelBaseUrl = it.modelFileUrl ?? '';
  }
};
// 选择数据集
const onSelectData = (value: any) => {
  let it = dataInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.datasetsName) {
    dataForm.datasetsId = value;
    dataForm.datasetsName = it.datasetsName ?? '';
    dataForm.datasetsUrl = it.datasetsFileUrl ?? '';
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/trainmodel/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/trainmodel", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
