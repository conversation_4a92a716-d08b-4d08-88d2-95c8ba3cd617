body {
  --color-primary: #409eff;
  --color-primary-light: rgba(64, 158, 255, 0.08);
}

/* 解决 Safari 和其他浏览器的兼容性问题 */
.webkit-prefixed {
  /* 修复 user-select 兼容性 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 修复 appearance 兼容性 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  /* 修复 backdrop-filter 兼容性 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

/* 全局应用一些常用的兼容性样式 */
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 如有需要嵌套的交互控件，确保正确处理 */
button:focus, 
input:focus, 
select:focus, 
textarea:focus {
  outline: none;
}

/* 强制修复错误提示中的ARIA属性问题 */
[role="button"] {
  cursor: pointer;
}

/* 确保所有带有role的元素都有适当的名称 */
[role]:not([aria-label]):not([aria-labelledby]) {
  /* 这只是一个占位符，实际应用中需要具体处理 */
  position: relative;
}
