<template>
  <el-dialog v-model="visible" align-center :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
      <el-form-item :label="$t('edge.sname')" prop="sname">
        <el-input v-model="dataForm.sname" :placeholder="$t('edge.sname')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.nodeMacAddress')" prop="saddress">
        <el-input v-model="dataForm.saddress" :placeholder="$t('edge.nodeMacAddress')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.interfaceName')" prop="sinterface">
        <el-input v-model="dataForm.sinterface" :placeholder="$t('edge.interfaceName')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.networkCardSpeed')" prop="snicSpeed">
        <el-input v-model="dataForm.snicSpeed" :placeholder="$t('edge.networkCardSpeed')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.ipv4Address')" prop="sv4Address">
        <el-input v-model="dataForm.sv4Address" :placeholder="$t('edge.ipv4Address')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.ipv4Gateway')" prop="sv4Gateway">
        <el-input v-model="dataForm.sv4Gateway" :placeholder="$t('edge.ipv4Gateway')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.ipv4Method')" prop="sv4Method">
        <el-input v-model="dataForm.sv4Method" :placeholder="$t('edge.ipv4Method')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('edge.ipv4SubnetMask')" prop="sv4Netmask">
        <el-input v-model="dataForm.sv4Netmask" :placeholder="$t('edge.ipv4SubnetMask')"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  sname: "",
  saddress: "",
  sinterface: "",
  snicSpeed: "",
  sv4Address: "",
  sv4Gateway: "",
  sv4Method: "",
  sv4Netmask: ""
});

const rules = ref({
  sname: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  saddress: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sinterface: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  snicSpeed: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Address: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Gateway: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Method: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  sv4Netmask: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});
const initForm = initDataForm(dataForm);
const init = (id?: string) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: string) => {
  baseService.get("/edge/tnodeinfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tnodeinfo/", dataForm).then(() => {
      ElMessage.success({
        message: $t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
