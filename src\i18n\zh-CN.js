const t = {}

t.loading = '加载中...'

t.brand = {}
t.brand.lg = '视频分析管理平台'
t.brand.mini = 'ucas'

t.add = '新增'
t.delete = '删除'
t.download = '下载'
t.deleteBatch = '删除'
t.update = '修改'
t.play = '播放'
t.query = '查询'
t.export = '导出'
t.handle = '操作'
t.confirm = '确定'
t.cancel = '取消'
t.clear = '清除'
t.logout = '退出'
t.manage = '处理'
t.createDate = '创建时间'
t.keyword = '关键字：'
t.choose = '请选择'
t.start = '开始'
t.stop = '停止'
t.restart = '重启'
t.status = '状态'
t.edit = '编辑'
t.save = '保存'

t.prompt = {}
t.prompt.title = '提示'
t.prompt.info = '确定进行[{handle}]操作?'
t.prompt.success = '操作成功'
t.prompt.failed = '操作失败'
t.prompt.deleteBatch = '请选择删除项'

t.validate = {}
t.validate.required = '必填项不能为空'
t.validate.format = '{attr}格式错误'

t.upload = {}
t.upload.text = '将文件拖到此处，或<em>点击上传</em>'
t.upload.tip = '只支持{format}格式文件！'
t.upload.button = '点击上传'

t.datePicker = {}
t.datePicker.range = '至'
t.datePicker.start = '开始日期'
t.datePicker.end = '结束日期'

t.fullscreen = {}
t.fullscreen.prompt = '您的浏览器不支持此操作'

t.updatePassword = {}
t.updatePassword.title = '修改密码'
t.updatePassword.loginOut = '退出登录'
t.updatePassword.username = '账号'
t.updatePassword.password = '原密码'
t.updatePassword.newPassword = '新密码'
t.updatePassword.confirmPassword = '确认密码'
t.updatePassword.validate = {}
t.updatePassword.validate.confirmPassword = '确认密码与新密码输入不一致'

t.contentTabs = {}
t.contentTabs.closeCurrent = '关闭当前标签页'
t.contentTabs.closeOther = '关闭其它标签页'
t.contentTabs.closeAll = '关闭全部标签页'
t.contentTabs.more = '更多'
t.contentTabs.hide = '隐藏'

/* 页面 */
t.notFound = {}
t.notFound.desc = '抱歉！您访问的页面<em>失联</em>啦...'
t.notFound.back = '上一页'
t.notFound.home = '首页'

t.login = {}
t.login.title = '登录'
t.login.username = '用户名'
t.login.password = '密码'
t.login.captcha = '验证码'
t.login.demo = '在线演示'
t.login.copyright = 'ucas'

t.edge = {}
// home
t.edge.processor = "处理器"
t.edge.cores = '核'
t.edge.memory = "内存";
t.edge.storage = "存储";
t.edge.temperature = "温度";
t.edge.runtime = "运行时间";

// 节点管理
t.edge.sname = '节点名称'
t.edge.nodeMacAddress = '设备节点网络mac地址'
t.edge.interfaceName = '接口名称'
t.edge.networkCardSpeed = '网卡速度';
t.edge.ipv4Address = 'IPv4地址';
t.edge.ipv4Gateway = 'IPv4网关';
t.edge.ipv4Method = 'IPv4方法';
t.edge.ipv4SubnetMask = 'IPv4掩码';
//设备管理
t.edge.nodeID = '节点ID';
t.edge.cache = '缓存';
t.edge.cpuLoad1 = 'CPU负载1';
t.edge.cpuLoad15 = 'CPU负载15';
t.edge.cpuLoad5 = 'CPU负载5';
t.edge.cpuCores = 'CPU核数';
t.edge.freeMemory = '空闲内存';
t.edge.sharedMemory = '共享内存';
t.edge.temperature = '温度';
t.edge.totalDisk = '磁盘总量';
t.edge.totalMemory = '内存总量';
t.edge.uptime = '运行时间';
t.edge.usedDisk = '已用磁盘';
t.edge.computingPower = '算力'
// 网络管理
t.edge.physicalAddress = "物理地址"
t.edge.duplex = "双工"
t.edge.power = "电源"
t.edge.networkCardSpeed = "网卡速度"
t.edge.networkCardSpeedSupport = "网卡速度支持"
t.edge.creationTime = "创建时间"
t.edge.updateTime = "更新时间"
t.edge.deletionTime = "删除时间"
t.edge.onOrOff = '开启/关闭'
t.edge.chooseIPv4Method = "选择IPv4方法"
t.edge.selectActiveArea = "请选择活动区域"
t.edge.autoGet = "自动获取";
t.edge.manualConfig = "手动配置";
t.edge.unknown = "未知";
// 摄像头管理
t.edge.name = "名称"
t.edge.cameraIPAddress = "摄像头IP地址";
t.edge.type = "类型";
t.edge.cameraManufacturer = "摄像头制造商";
t.edge.videoStreamAddress1 = "视频流地址1";
t.edge.cameraID = "摄像头ID";
t.edge.username = "用户名";
t.edge.password = "用户密码";
t.edge.longitude = "经度";
t.edge.latitude = "纬度";
// 布控管理
t.edge.playVideo = "播放";
t.edge.stopVideo = "停止";
t.edge.clearControlArea = "清除布控区域";
t.edge.addControlAreaRec = "绘制布控区域";
t.edge.viewAreaCoordinates = "查看区域坐标";
// 模型管理
t.edge.modelName = "模型名称";
t.edge.modelFileName = "模型文件名";
t.edge.modelSize = "模型大小";
t.edge.labelFileName = "标签文件名";
t.edge.selectModel = "选择模型";
t.edge.labelModel = "标签模型";
t.edge.datasetsName = "数据集";
t.edge.selectNode = "选择节点";
// 推送管理
t.edge.pushName = "推送名称";
t.edge.pushURL = "推送URL";
// 链路管理
t.edge.linkName = "链路名称";
t.edge.cameraName = "摄像头名称";
t.edge.cameraUUID = "摄像头UUID";
t.edge.allNodesLinkNodes = "所有节点LinkNodes";
t.edge.model = '模型';
t.edge.push = '推送';
t.edge.pleaceAddNode = '请添加节点'
t.edge.addNode = '添加节点'
t.edge.fillModelAndPushInfo = "请填写模型和推送的信息：";
t.edge.targetTracking = "目标跟踪";
t.edge.frameByFrameDetection = "隔帧检测";
t.edge.roiRegion = "ROI区域";
t.edge.pushMultiple = "推送(可多选)";
t.edge.selectPush = "推送选择";
t.edge.confirmAddNode = "确认添加节点";
// 链路部署
t.edge.linkID = "链路ID";
t.edge.linkStatus = "链路状态";
t.edge.deployStatus = "部署状态";
t.edge.securityLevel = "安全预警类型";
t.edge.modelDeploymentPath = "模型部署路径";
t.edge.parameterConfiguration = "参数配置";
t.edge.parameterConfigurationInfo = "参数配置信息";
t.edge.parameterConfigurationConfirm = '请输入正确的参数配置信息'
t.edge.remarks = "备注";
t.edge.showing = '查看'
t.edge.selectLink = "链路选择"
t.edge.linkStatusContent = '链路状态: 1. 部署中；2. 部署完成； 3. 运行中； 4. 节点停止； 5. 节点丢失； 99：异常'
t.edge.deployStatusContent = '部署状态 0. 准备中；1. 部署中；2. 部署完成； 3. 运行中； 4. 节点丢失； 99：异常'
t.edge.safeTypeContent = '安全预警类型'
// 检测数据
t.edge.primaryKeyID = "告警id";
t.edge.serialNumber = "序列号";
t.edge.deviceID = "设备ID";
t.edge.originalImageURL = "原始图片地址";
t.edge.boundingBoxImageURL = "画框图片地址";
t.edge.keyword = "关键字";
t.edge.videoID = "视频ID";
t.edge.fileFormat = "文件格式";
t.edge.timeSort = "时间排序";
t.edge.alertLevel = "告警类型";
t.edge.contentDescription = "内容描述";
t.edge.deploymentLocation = "部署地点";
t.edge.deploymentLocationLongitude = "部署地点经度";
t.edge.deploymentLocationLatitude = "部署地点纬度";
t.edge.cameraIndexCode = "摄像头索引码";
t.edge.detectionTime = "检测时间";
t.edge.targetListData = "目标列表数据";
t.edge.behaviorDescription = "0: 目标检测无异常,1: 聚集,3: 冲卡,4: 持械,5: 打架,7:违停,8: 逆行,13: 跌倒"
t.edge.anomalyBehavior = "异常行为描述, 异常行为标记"
t.edge.targetListDataContent = "类别：car，person，bus等, 目标坐标，异常行为检测无坐标，直接传原始帧"
t.edge.time = "时间";
t.edge.securityLevelContent = `5: 人车检测、小目标检测,\n4: 跌倒，违停等,\n3: 打架、持械等`

t.schedule = {}
t.schedule.beanName = 'bean名称'
t.schedule.beanNameTips = 'spring bean名称, 如: testTask'
t.schedule.pauseBatch = '暂停'
t.schedule.resumeBatch = '恢复'
t.schedule.runBatch = '执行'
t.schedule.log = '日志列表'
t.schedule.params = '参数'
t.schedule.cronExpression = 'cron表达式'
t.schedule.cronExpressionTips = '如: 0 0 12 * * ?'
t.schedule.remark = '备注'
t.schedule.status = '状态'
t.schedule.status0 = '暂停'
t.schedule.status1 = '正常'
t.schedule.statusLog0 = '失败'
t.schedule.statusLog1 = '成功'
t.schedule.pause = '暂停'
t.schedule.resume = '恢复'
t.schedule.run = '执行'
t.schedule.jobId = '任务ID'
t.schedule.times = '耗时(单位: 毫秒)'
t.schedule.createDate = '执行时间'

t.oss = {}
t.oss.config = '云存储配置'
t.oss.upload = '上传文件'
t.oss.url = 'URL地址'
t.oss.createDate = '创建时间'
t.oss.type = '类型'
t.oss.type1 = '七牛'
t.oss.type2 = '阿里云'
t.oss.type3 = '腾讯云'
t.oss.qiniuDomain = '域名'
t.oss.qiniuDomainTips = '七牛绑定的域名'
t.oss.qiniuPrefix = '路径前缀'
t.oss.qiniuPrefixTips = '不设置默认为空'
t.oss.qiniuAccessKey = 'AccessKey'
t.oss.qiniuAccessKeyTips = '七牛AccessKey'
t.oss.qiniuSecretKey = 'SecretKey'
t.oss.qiniuSecretKeyTips = '七牛SecretKey'
t.oss.qiniuBucketName = '空间名'
t.oss.qiniuBucketNameTips = '七牛存储空间名'
t.oss.aliyunDomain = '域名'
t.oss.aliyunDomainTips = '阿里云绑定的域名'
t.oss.aliyunPrefix = '路径前缀'
t.oss.aliyunPrefixTips = '不设置默认为空'
t.oss.aliyunEndPoint = 'EndPoint'
t.oss.aliyunEndPointTips = '阿里云EndPoint'
t.oss.aliyunAccessKeyId = 'AccessKeyId'
t.oss.aliyunAccessKeyIdTips = '阿里云AccessKeyId'
t.oss.aliyunAccessKeySecret = 'AccessKeySecret'
t.oss.aliyunAccessKeySecretTips = '阿里云AccessKeySecret'
t.oss.aliyunBucketName = 'BucketName'
t.oss.aliyunBucketNameTips = '阿里云BucketName'
t.oss.qcloudDomain = '域名'
t.oss.qcloudDomainTips = '腾讯云绑定的域名'
t.oss.qcloudPrefix = '路径前缀'
t.oss.qcloudPrefixTips = '不设置默认为空'
t.oss.qcloudAppId = 'AppId'
t.oss.qcloudAppIdTips = '腾讯云AppId'
t.oss.qcloudSecretId = 'SecretId'
t.oss.qcloudSecretIdTips = '腾讯云SecretId'
t.oss.qcloudSecretKey = 'SecretKey'
t.oss.qcloudSecretKeyTips = '腾讯云SecretKey'
t.oss.qcloudBucketName = 'BucketName'
t.oss.qcloudBucketNameTips = '腾讯云BucketName'
t.oss.qcloudRegion = '所属地区'
t.oss.qcloudRegionTips = '请选择'
t.oss.qcloudRegionBeijing1 = '北京一区（华北）'
t.oss.qcloudRegionBeijing = '北京'
t.oss.qcloudRegionShanghai = '上海（华东）'
t.oss.qcloudRegionGuangzhou = '广州（华南）'
t.oss.qcloudRegionChengdu = '成都（西南）'
t.oss.qcloudRegionChongqing = '重庆'
t.oss.qcloudRegionSingapore = '新加坡'
t.oss.qcloudRegionHongkong = '香港'
t.oss.qcloudRegionToronto = '多伦多'
t.oss.qcloudRegionFrankfurt = '法兰克福'

t.dept = {}
t.dept.name = '名称'
t.dept.parentName = '上级部门'
t.dept.sort = '排序'
t.dept.parentNameDefault = '一级部门'
t.dept.chooseerror = '请选择部门'
t.dept.title = '选择部门'

t.dict = {}
t.dict.dictName = '字典名称'
t.dict.dictType = '字典类型'
t.dict.dictLabel = '字典标签'
t.dict.dictValue = '字典值'
t.dict.sort = '排序'
t.dict.remark = '备注'
t.dict.createDate = '创建时间'

t.logError = {}
t.logError.requestUri = '请求URI'
t.logError.requestMethod = '请求方式'
t.logError.requestParams = '请求参数'
t.logError.ip = '操作IP'
t.logError.userAgent = '用户代理'
t.logError.createDate = '创建时间'
t.logError.errorInfo = '异常信息'

t.logLogin = {}
t.logLogin.creatorName = '用户名'
t.logLogin.status = '状态'
t.logLogin.status0 = '失败'
t.logLogin.status1 = '成功'
t.logLogin.status2 = '账号已锁定'
t.logLogin.operation = '操作类型'
t.logLogin.operation0 = '登录'
t.logLogin.operation1 = '退出'
t.logLogin.ip = '操作IP'
t.logLogin.userAgent = 'User-Agent'
t.logLogin.createDate = '创建时间'

t.logOperation = {}
t.logOperation.status = '状态'
t.logOperation.status0 = '失败'
t.logOperation.status1 = '成功'
t.logOperation.creatorName = '用户名'
t.logOperation.operation = '用户操作'
t.logOperation.requestUri = '请求URI'
t.logOperation.requestMethod = '请求方式'
t.logOperation.requestParams = '请求参数'
t.logOperation.requestTime = '请求时长'
t.logOperation.ip = '操作IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = '创建时间'

t.menu = {}
t.menu.name = '名称'
t.menu.icon = '图标'
t.menu.type = '类型'
t.menu.type0 = '菜单'
t.menu.type1 = '按钮'
t.menu.sort = '排序'
t.menu.url = '路由'
t.menu.permissions = '授权标识'
t.menu.permissionsTips = '多个用逗号分隔，如：sys:menu:save,sys:menu:update'
t.menu.parentName = '上级菜单'
t.menu.parentNameDefault = '一级菜单'
t.menu.resource = '授权资源'
t.menu.resourceUrl = '资源URL'
t.menu.resourceMethod = '请求方式'
t.menu.resourceAddItem = '添加一项'
t.menu.home = '首页'
t.menu.algorithm = '算法'
t.menu.model = '已部署场景'
t.menu.runningModel = '运行中场景'
t.menu.stream = '已接入视频'
t.menu.alarm = '告警总数'
t.menu.monitor = '系统监控'
t.menu.cronjob = '定时任务'
t.menu.website = '站点'
t.menu.database = '数据库'
t.menu.todayAlarm = '今日告警'
t.menu.runningStreamCount = '运行中视频流'

t.params = {}
t.params.paramCode = '编码'
t.params.paramValue = '值'
t.params.remark = '备注'

t.role = {}
t.role.name = '名称'
t.role.remark = '备注'
t.role.createDate = '创建时间'
t.role.menuList = '菜单授权'
t.role.deptList = '数据授权'

t.user = {}
t.user.username = '用户名'
t.user.deptName = '所属部门'
t.user.email = '邮箱'
t.user.mobile = '手机号'
t.user.status = '状态'
t.user.status0 = '停用'
t.user.status1 = '正常'
t.user.createDate = '创建时间'
t.user.password = '密码'
t.user.confirmPassword = '确认密码'
t.user.realName = '真实姓名'
t.user.gender = '性别'
t.user.gender0 = '男'
t.user.gender1 = '女'
t.user.gender2 = '保密'
t.user.roleIdList = '角色配置'
t.user.validate = {}
t.user.validate.confirmPassword = '确认密码与密码输入不一致'
t.user.select = '选择用户'
t.user.selecterror = '请选择一条记录'

t.home = {
  restart_1panel: '重启面板',
  restart_system: '重启服务器',
  operationSuccess: '操作成功，正在重启，请稍后手动刷新浏览器！',
  overview: '概览',
  entranceHelper: '设置安全入口有利于提高系统的安全性，如有需要，前往 面板设置-安全 中，启用安全入口',
  appInstalled: '已安装应用',
  systemInfo: '系统信息',
  hostname: '主机名称',
  platformVersion: '发行版本',
  kernelVersion: '内核版本',
  kernelArch: '系统类型',
  network: '流量',
  io: '磁盘 IO',
  ip: '主机地址',
  proxy: '系统代理',
  baseInfo: '基本信息',
  totalSend: '总发送',
  totalRecv: '总接收',
  rwPerSecond: '读写次数',
  ioDelay: '读写延迟',
  uptime: '启动时间',
  runningTime: '运行时间',
  mem: '系统',
  swapMem: 'Swap 分区',
  alarmStatistics: '告警统计',

  runSmoothly: '运行流畅',
  runNormal: '运行正常',
  runSlowly: '运行缓慢',
  runJam: '运行堵塞',

  core: '物理核心',
  logicCore: '逻辑核心',
  loadAverage: '最近 1 分钟平均负载 | 最近 {n} 分钟平均负载',
  load: '负载',
  mount: '挂载点',
  fileSystem: '文件系统',
  total: '总数',
  used: '已用',
  free: '可用',
  percent: '使用率',
  app: '推荐应用',
  goInstall: '去安装',

  networkCard: '网卡',
  disk: '磁盘',
  physicalCores: '物理核心',
  logicalCores: '逻辑核心',
  loadAvg: '负载',
  loadAverage: '负载 {count} 分钟',
  mount: '挂载点',
  fileSystem: '文件系统',
  baseInfo: '基本信息',
  mem: '内存',
  total: '总计',
  used: '已用',
  free: '可用',
  percent: '使用率',
  swapMem: '交换内存',
  runSmoothly: '运行流畅',
  runNormal: '运行正常',
  runSlowly: '运行缓慢',
  runJam: '运行卡顿'
}

// 添加 commons 相关翻译
t.commons = {
    table: {
        status: '状态监控',
        type: '类型',
        all: '全部'
    },
    button: {
        showAll: '显示所有',
        hideSome: '隐藏部分'
    },
    units: {
        core: '{count} 核',
        dayUnit: '天',
        hourUnit: '小时',
        minuteUnit: '分',
        secondUnit: '秒'
        // time: '次' // This might have been from the old structure, check if needed
    }
};

// 添加 monitor 相关翻译
t.monitor = {
    up: '上传',
    down: '下载',
    read: '读取',
    write: '写入',
    path: '路径',
    type: '类型',
    total: '总计',
    used: '已用',
    free: '可用',
    usedPercent: '使用率',
    index: '索引',
    productName: '产品名称',
    gpuUtil: 'GPU利用率',
    temperature: '温度',
    performanceState: '性能状态',
    powerUsage: '功耗',
    memoryUsage: '显存使用',
    fanSpeed: '风扇转速',
    memory: '内存',
    disk: '磁盘'
}

// Add tabs translations
t.tabs = {
    more: '更多',
    hide: '隐藏'
};

export default t
