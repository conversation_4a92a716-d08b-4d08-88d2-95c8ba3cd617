import baseService from "@/service/baseService";
import app from "@/constants/app";
//媒体注册
export const mediaServerRegister = async (streamUrl?: string, channelId?: string): Promise<void> => {
  if (await mediaListInfo(channelId)) {
    return
  }
  return new Promise((resolve, reject) => {
    baseService.post('/index/api/addStreamProxy',
      {
        secret: app.media_secret,
        vhost: '__defaultVhost__',
        app: 'live',
        stream: channelId,
        url: streamUrl,
        rtp_type: 'udp',
        enable_audio: true,
        enable_hls: true,
        enable_hls_fmp4: true,
        enable_mp4: true,
        enable_rtsp: true,
        enable_ts: true,
        enable_fmp4: true,
        hls_demand: true,
        rtsp_demand: true,
        rtmp_demand: true,
        ts_demand: true,
        fmp4_demand: true,
        auto_close: false,
      }
    ).then((res) => {
      console.log(res.data);
      resolve();
    }).catch((error) => {
      console.error('Error registering media server:', error);
      reject(error);
    });
  });
};
//查看媒体注册信息
export const mediaListInfo = async (channel?: string): Promise<any> => {
  try {
    if (!channel) {
      const response = await baseService.post('/index/api/getMediaList', {
        secret: app.media_secret,
      });
      if (response.data) {
        return response.data[0];
      }
    }
    else {
      const response = await baseService.post('/index/api/getMediaList', {
        secret: app.media_secret,
        stream: channel
      });
      return !!response.data;
    }
  } catch (error) {
    console.error('Error fetching media list info:', error);
    throw error;
  }
};
// 退出时通知关闭推流
/*export const handleBeforeUnload = () =>{
  const url = 'http:192.168.1.66:35007/index/api/close_streams';
  const data = new Blob([JSON.stringify({
    secret: app.media_secret,
    force: true,
  })], {type: 'application/json'});
  const success = navigator.sendBeacon(url, data);

  if (success) {
    console.log('数据成功发送');
  } else {
    console.log('数据发送失败');
  }
}*/
