<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <!-- <el-form-item prop="beanName" label="操作">
         <el-select v-model="dataForm.beanName" placeholder="请选择操作">
            <el-option v-for="item in SBeanName" :key="item.id" :label="item.name" :value="item.beanName"></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item label="镜像名称" prop="params">
        <el-select class="m-2" v-model="dataForm.params" value-key="id" @change="Selected" placeholder="请选择镜像名称">
          <el-option v-for="item in imageInfo" :key="item.id" :label="item.trainName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item prop="params" label="镜像ID">
        <el-input v-model="dataForm.params" placeholder="镜像ID" disabled></el-input>
      </el-form-item>

      <el-form-item label="摄像头">
        <el-input v-model="deviceId" placeholder="摄像头名称" disabled></el-input>
      </el-form-item>

      <el-form-item prop="cronExpression" label="定时任务">
        <!-- <el-input v-model="dataForm.cronExpression" placeholder="可选择日期自动生成"></el-input>              -->
        <!-- <div class="demo-datetime-picker"> -->
        <div class="block">
          <!-- <span class="demonstration">选择时间</span> -->
          <!-- 固定时间执行：
      <el-date-picker
        @change="handleSelect"
        v-model="value1"
        type="datetime"
        placeholder="请选择时间"
      /> -->

          <span style="margin-left: 10px">开始时间：<el-time-picker style="width: 120px" v-model="value1" placeholder="开始时间" @change="handleSelect1" /></span>
          <span style="margin-left: 10px">停止时间：<el-time-picker style="width: 120px" v-model="value2" placeholder="停止时间" @change="handleSelect2" /></span>
          <!-- </div> -->
        </div>
      </el-form-item>

      <!-- <el-form-item prop="remark" label="备注">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item> -->
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);
const value1 = ref("");
const value2 = ref("");
const cron1 = ref("");
const cron2 = ref("");
const visible = ref(false);
const dataFormRef = ref();
const imageInfo = ref<ImagesInfo[]>([]); // 训练列表
const SBeanName = ref<SBeanName[]>([]);
const deviceId = ref(""); // 设备id

export interface ImagesInfo {
  id: string | number;
  trainName: string;
  deviceId: string;
}
export interface SBeanName {
  id: string | number;
  name: string;
  beanName: string;
}
const Selected = (val: string) => {
  //用val去跟imageInfo数组里的id匹配，然后赋值给dataForm.params
  deviceId.value = imageInfo.value.find((item) => item.id === val)?.deviceId || "";
};
SBeanName.value = [
  { id: 1, name: "定时启动", beanName: "modelTask" },
  { id: 2, name: "定时关闭", beanName: "stopTask" }
];
const dataForm = reactive({
  id: "",
  beanName: "modelTask",
  params: "",
  cronExpression: "",
  remark: "",
  status: 0
});
baseService.get("/edge/predictdeploy/page", { limit: 10000 }).then((res) => {
  // dataForm.trainName = res.data.trainName;
  dataForm.id = res.data.id;
});
const rules = ref({
  // beanName: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
  // cronExpression: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

baseService.get("/edge/predictdeploy/page", { limit: 10000 }).then((images) => {
  imageInfo.value = images.data.list;
});
// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/schedule/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};
//选择时间之后的处理
const handleSelect = (date: any) => {
  //将date转换为cron表达式
  dataForm.cronExpression = `${date.getSeconds()} ${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} ?`;
  value2.value = "";
};
//选择每天定时执行时间之后的处理
const handleSelect1 = (date: any) => {
  //将date转换为cron表达式
  cron1.value = `${date.getSeconds()} ${date.getMinutes()} ${date.getHours()} * * ?`;
};
const handleSelect2 = (date: any) => {
  //将date转换为cron表达式
  cron2.value = `${date.getSeconds()} ${date.getMinutes()} ${date.getHours()} * * ?`;
};
// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if (cron1.value && cron2.value) {
      dataForm.cronExpression = cron1.value;
      dataForm.beanName = "modelTask";
      dataForm.remark = "定时启动";
      baseService.post("/sys/schedule", dataForm).then((res) => {
        dataForm.cronExpression = cron2.value;
        dataForm.beanName = "stopTask";
        dataForm.remark = "定时关闭";
        baseService.post("/sys/schedule", dataForm).then((res) => {
          ElMessage.success({
            message: "成功",
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        });
      });
    }
  });
};

defineExpose({
  init
});
</script>

<style lang="less">
.schedule-cron {
  &-popover {
    width: auto !important;
    min-width: 550px !important;
  }
}
.demo-datetime-picker {
  display: flex;
  width: 100%;
  padding: 0;
  flex-wrap: wrap;
}
.demo-datetime-picker .block {
  padding: 3px 0;
  // text-align: center;
  border-right: solid 1px var(--el-border-color);
  flex: 1;
}
.demo-datetime-picker .block:last-child {
  border-right: none;
}
.demo-datetime-picker .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
</style>
