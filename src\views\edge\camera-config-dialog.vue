<template>
  <el-dialog
    :title="`配置分组 '${groupName}' 的摄像头`"
    v-model="dialogVisible"
    @close="handleClose"
    @opened="onDialogOpened"
    width="700px"
  >
    <div v-loading="loading" style="min-height: 300px;" class="transfer-wrapper">
      <el-transfer
        v-model="targetKeys"
        :data="transferData"
        :titles="['所有摄像头', '已分配到此分组']"
        :filterable="true"
        filter-placeholder="请输入摄像头名称搜索"
        :props="{ key: 'id', label: 'name' }"
        style="text-align: left; display: inline-block; width: 100%;"
      >
        <template #default="{ option }">
          <span>{{ option.name }}</span>
        </template>
      </el-transfer>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import baseService from '@/service/baseService';
import { updateCameraGrouping, getCameraInfoByGroupId } from '@/utils/api';

interface CameraInfo {
  id: string;
  name: string;
  groupId: string | null;
  groupName?: string | null;
  // 如果需要，在此处添加其他用于显示或逻辑的摄像头属性
  disabled?: boolean;
}

interface TransferDataItem {
  id: string; // 使用摄像头ID作为key
  name: string; // 使用摄像头名称作为label
  disabled?: boolean;
}

// 新增：定义后端期望的 DTO 接口
interface CameraGroupUpdateDTO {
  id: string; // 摄像头ID
  name: string; // 摄像头名称
  gId: string | null; // 要分配的分组ID，或null表示取消分配
  gName: string | null; // 要分配的分组名称，或null
}

const props = defineProps({
  modelValue: Boolean, // 用于v-model
  groupId: {
    type: String,
    required: true
  },
  groupName: { // 用于在标题中显示
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

const dialogVisible = ref(false);
const loading = ref(false);
const saving = ref(false);
const allCameraList = ref<CameraInfo[]>([]);    // 所有摄像头列表
const transferData = ref<TransferDataItem[]>([]); // 用于el-transfer的渲染数据
const targetKeys = ref<string[]>([]); // 保存分配给该分组的摄像头的ID
const initialTargetKeys = ref<string[]>([]); // 保存对话框打开时，该分组已有的摄像头ID

watch(() => props.modelValue, (newValue) => {
//   console.log("[DialogDebug] props.modelValue changed to:", newValue);
  dialogVisible.value = newValue;
//   console.log("[DialogDebug] internal dialogVisible set to:", dialogVisible.value);
});

onMounted(() => {
//   console.log("[DialogDebug] CameraConfigDialog MOUNTED. Initial props.modelValue:", props.modelValue, "Initial internal dialogVisible:", dialogVisible.value);
  // watch应该能处理这个问题，但作为备选方案，如果 modelValue 在挂载时为 true:
  if (props.modelValue && !dialogVisible.value) {
    dialogVisible.value = true;
    // console.log("[DialogDebug] Internal dialogVisible explicitly set to true on mount because props.modelValue was true."); // 因为props.modelValue为true，在挂载时显式将内部dialogVisible设置为true
  }
});

const onDialogOpened = async () => {
  if (!props.groupId) {
    ElMessage.error("分组ID无效，无法加载摄像头配置。");
    handleClose();
    return;
  }
  loading.value = true;
  allCameraList.value = []; // 重置所有摄像头列表
  transferData.value = [];  // 重置穿梭框的源数据
  targetKeys.value = [];    // 重置目标key (当前分组中的摄像头ID)
  initialTargetKeys.value = []; // 重置初始目标key

  try {
    // 并行获取所有摄像头列表和当前分组的摄像头列表
    const groupIdAsNumber = parseInt(props.groupId, 10); // 转换为数字
    if (isNaN(groupIdAsNumber)) {
        ElMessage.error("无效的分组ID格式，无法加载摄像头。");
        throw new Error("Invalid groupId format");
    }

    const [allCamerasRes, groupCamerasAxiosRes] = await Promise.all([
      baseService.get("/edge/tcamerainfo/AllInfoList"), // 获取所有摄像头
      getCameraInfoByGroupId(groupIdAsNumber) // 使用转换后的数字ID
    ]);

    // 1. 处理所有摄像头列表 (用于穿梭框左侧数据源)
    if (allCamerasRes && allCamerasRes.code === 0 && Array.isArray(allCamerasRes.data)) {
      allCameraList.value = allCamerasRes.data;
      transferData.value = allCameraList.value.map(cam => ({
        id: cam.id,
        name: cam.name || `未命名摄像头 (ID: ${cam.id})`,
      }));
    } else {
      ElMessage.error(allCamerasRes.msg || "加载所有摄像头列表失败");
      // 即使所有摄像头列表加载失败，也可能需要继续尝试加载分组摄像头
    }

    // 2. 处理当前分组的摄像头列表 (用于穿梭框右侧的初始选中项)
    let groupCameras: CameraInfo[] = [];
    // 假设 getCameraInfoByGroupId 返回 Promise<AxiosResponse<BackendResponse<CameraInfo[]>>>
    if (groupCamerasAxiosRes && groupCamerasAxiosRes.data) {
      const groupCamerasBackendData = groupCamerasAxiosRes.data;
      if (groupCamerasBackendData && groupCamerasBackendData.code === 0 && Array.isArray(groupCamerasBackendData.data)) {
        groupCameras = groupCamerasBackendData.data;
      } else {
        ElMessage.error(groupCamerasBackendData?.msg || "加载分组内摄像头列表失败");
      }
    } else {
      ElMessage.error("加载分组内摄像头列表的响应无效或数据缺失");
    }
    
    targetKeys.value = groupCameras.map(cam => cam.id);
    initialTargetKeys.value = [...targetKeys.value]; // 存储初始选中的摄像头ID

  } catch (error) {
    console.error("加载摄像头配置时发生错误:", error);
    ElMessage.error("加载摄像头数据时出错，详情请查看控制台。");
    // 清空数据以避免显示不一致的状态
    allCameraList.value = [];
    transferData.value = [];
    targetKeys.value = [];
    initialTargetKeys.value = [];
  } finally {
    loading.value = false;
  }
};

const handleSave = async () => {
  saving.value = true;
  try {
    const currentAssignedIdsSet = new Set(targetKeys.value);
    const initiallyAssignedIdsSet = new Set(initialTargetKeys.value);

    const addedCameraIds = targetKeys.value.filter(id => !initiallyAssignedIdsSet.has(id));
    const removedCameraIds = initialTargetKeys.value.filter(id => !currentAssignedIdsSet.has(id));

    console.log(`为分组ID ${props.groupId} (${props.groupName}) 操作详情:`);
    console.log("初始摄像头IDs:", initialTargetKeys.value);
    console.log("最终摄像头IDs:", targetKeys.value);
    console.log("新增的摄像头IDs:", addedCameraIds);
    console.log("移除的摄像头IDs:", removedCameraIds);

    const operationsList: CameraGroupUpdateDTO[] = [];

    // 准备新增摄像头的 DTO
    for (const camId of addedCameraIds) {
      const camera = allCameraList.value.find(c => c.id === camId);
      if (camera) {
        operationsList.push({
          id: camId,
          name: camera.name || '未知名称', // 确保有name值
          gId: props.groupId,
          gName: props.groupName
        });
      }
    }

    // 准备移除摄像头的 DTO
    for (const camId of removedCameraIds) {
      const camera = allCameraList.value.find(c => c.id === camId);
      if (camera) {
        operationsList.push({
          id: camId,
          name: camera.name || '未知名称', // 确保有name值
          gId: null, // 设置分组ID为null以移除
          gName: null // 设置分组名称为null
        });
      }
    }

    if (operationsList.length > 0) {
      console.log("批量更新摄像头分组，发送的 DTO 列表:", JSON.parse(JSON.stringify(operationsList)));
      
      // 显式定义期望的后端响应体结构
      interface BackendResponse { code: number; msg: string; data?: any; }

      try {
        const axiosResponse = await updateCameraGrouping(operationsList);
        // 假设 updateCameraGrouping 返回 Promise<AxiosResponse<BackendResponse>>
        const backendData = axiosResponse.data;

        if (backendData && backendData.code === 0) {
          ElMessage.success("摄像头配置更新成功！");
          emit('saved');
          handleClose();
        } else {
          const errorMsg = backendData ? backendData.msg : "响应数据格式不正确或操作失败";
          ElMessage.error(errorMsg || "摄像头配置更新失败。");
          console.error("摄像头配置更新失败详情 (axiosResponse.data):", backendData);
          console.error("原始Axios响应 (axiosResponse):", axiosResponse);
        }
      } catch (apiError) {
        // 处理 updateCameraGrouping 内部可能抛出的网络错误或其他直接错误
        console.error("调用 updateCameraGrouping 时发生错误:", apiError);
        ElMessage.error("请求更新摄像头配置时发生网络或执行错误。");
      }
    } else {
      ElMessage.info("没有需要更新的摄像头配置。");
      handleClose(); // 如果没有更改也关闭对话框
    }

  } catch (error) {
    console.error("保存摄像头配置时出错:", error);
    ElMessage.error("保存摄像头配置时发生错误。");
  } finally {
    saving.value = false;
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  emit('update:modelValue', false);
};

</script>

<style scoped>
.transfer-wrapper {
  /* 这个div包裹了el-transfer。确保它允许el-transfer正确布局。 */
  /* 默认情况下，它是一个块级元素，会占据可用宽度。 */
}

.el-transfer {
  display: flex !important; /* 强制flex布局 */
  flex-direction: row !important; /* 强制行方向 */
  justify-content: center; /* 如果内部元素组未占满宽度，则居中显示它们 */
  align-items: center; /* 垂直居中对齐项目 */
  width: 100%; /* el-transfer尝试使用.transfer-wrapper的全部宽度 */
}

.el-transfer :deep(.el-transfer-panel) {
  width: 270px !important; /* 为面板设置固定宽度，可根据需要调整 */
  height: 300px; /* 设置统一的高度 */
  box-shadow: 0 0 5px rgba(0,0,0,0.1); /* 可选：添加一些阴影以获得更好的视觉分离 */
}

.el-transfer :deep(.el-transfer__buttons) {
  padding: 0 15px; /* 按钮组周围的内边距 */
  /* 按钮本身通常在此容器内垂直堆叠 */
}

/* 
  el-dialog 组件通过 prop 设置了 width="700px"。
  el-dialog__body 的实际内容区域通常是 width 减去左右内边距 (例如, 700px - 40px = 660px)。
  我们的面板 (270px * 2) + 按钮 (约 50-80px) 应该能容纳下：
  540px + ~65px = 605px，这小于 (700px - 40px = 660px)。
*/
</style>
