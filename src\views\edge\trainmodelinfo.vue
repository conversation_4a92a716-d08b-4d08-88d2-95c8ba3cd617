<template>
  <div class="mod-edge__trainmodelinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" show-overflow-tooltip style="width: 100%">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <!-- <el-table-column prop="id" label="ID" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="modelName" label="模型名称" header-align="center" align="center" width="200"></el-table-column>
      <el-table-column prop="modelFileName" label="模型文件名" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="modelFileUrl" label="模型文件地址" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="configs" label="参数配置" header-align="center" align="center"></el-table-column>
              <el-table-column prop="memo" label="备注" header-align="center" align="center"></el-table-column> -->
      <!-- <el-table-column prop="twfCreated" label="创建时间" header-align="center" align="center"></el-table-column>
              <el-table-column prop="twfModified" label="更新时间" header-align="center" align="center"></el-table-column> -->
      <!--              <el-table-column prop="twfDeleted" label="删除时间" header-align="center" align="center"></el-table-column>-->
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="warning" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./trainmodelinfo-add-or-update.vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/trainmodelinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/trainmodelinfo/export",
  deleteURL: "/edge/trainmodelinfo"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
