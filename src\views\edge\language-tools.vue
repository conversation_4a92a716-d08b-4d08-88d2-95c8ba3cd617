<template>
  <div class="common-layout">
    <el-container>
      <el-header><div class="input-tip">输入内容</div></el-header>
      <el-container style="height: 350px">
        <el-aside width="20px"></el-aside>
        <el-main>
          <div style="margin-top: -20px;"> <!-- 添加 margin-top 来调整距离 -->
            <el-space>

            </el-space>
            <div class="flex flex-wrap gap-4 items-center">
              <el-select
                v-model="value1"
                placeholder="选择模型"
                size="large"
                @change="handleSelectChange1"
              >
                <el-option
                  v-for="item in getAll"
                  :key="item.id"
                  :label="item.trainName"
                  :value="item.securityLevel"
                />
              </el-select>
              <!-- <div class="sf-label">选择算法</div> -->
              <el-select
                v-model="value3"
                placeholder="选择算法"
                size="large"
                @change="handleSelectChange"
                style="margin-left: 5px;"
              >
                <el-option
                v-for="item in getLabel()"
                :key="item.id"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </div>
          </div>
          <div style="margin: 20px 0" />
          <el-input v-model="textarea1" resize="none" style="width: 100%" rows="10" :placeholder="placeholder" type="textarea" maxlength="500" />
          <div style="display: flex;justify-content: space-between">
          <el-button type="primary" class="submit-btn" @click="submitContent">提交内容</el-button>
          <div class="custom-word-limit">
            <span>字数：</span>
            <span>{{ textarea1.length }} / 500</span>
          </div>
          </div>

        </el-main>
      </el-container>
    </el-container>
  </div>
  <div class="common-layout">
    <el-container>
      <el-header><div class="input-tip">算法结果</div></el-header>
      <el-container>
        <el-aside width="20px"></el-aside>
        <el-main>
          <div style="margin-top: -40px;"> <!-- 添加 margin-top 来调整距离 -->
          <div style="margin: 20px 0" /></div>
          <!-- <el-input
            v-model="textarea2"
            resize="none"
            style="width:100%;"
            rows="5"
            type="textarea"
            placeholder=""
          /> -->
          <div class="newT2"></div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import app from "@/constants/app";
import baseService from '@/service/baseService'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { onActivated, reactive, ref } from 'vue'
const textarea1 = ref('')
const textarea2 = ref('')
const value1 = ref('')
const value2 = ref('')
const value3 = ref('')
const getAll = ref('')

const options = reactive([
  {
    id: 39,
    value: '/entity_recognize',
    label: '实体识别',
  },
  {
    id: 35,
    value: '/get_keyphrase',
    label: '关键词提取',
  },
  {
    id: 37,
    value: '/part_of_speech',
    label: '词性标注',
  },
  {
    id: 38,
    value: '/sentiment_analysis',
    label: '情感分析',
  },
  {
    id: 41,
    value: '/summary_generation',
    label: '文本摘要',
  },
  {
    id: 40,
    value: '/text_classification',
    label: '文本分类',
  },
  {
    id: 36,
    value: '/text_similarity',
    label: '文本相似性分析',
  },
  {
    id: 34,
    value: '/word_segmentation',
    label: '中文分词',
  },
])
const getValue = (value) => {

  let result = ''
  for(let i = 0; i < options.length; i++){
    if(options[i].label === value){
      result = options[i].value
    }
  }
  return result
}
const getLabel = () => {
  let result = [];
  let new_value1 = value1.value.split(',');
  for(let j = 0; j < new_value1.length; j++){
    for(let i = 0; i < options.length; i++){
      if(options[i].id == new_value1[j]){
        result.push(options[i].label);
        break; // 找到匹配项后跳出内层循环，提高性能
      }
    }
  }
  // 将结果数组拼接成对象数组返回
  return result;
}

const entity_Recognize = {
  GPE:'地缘政治实体',
  PER:'人名',
  ORG:'机构名',
  LOC:'地名',
}
const pos_Tagging = {
// AD:	'adverbs,副词',
// M:	'Measure word(including classifiers),量词，例子："个"',
// AS:	'Aspect marke,体态词，体标记（例如：了，在，着，过）',
// MSP:	'Some particles,例子："所"',
// BA:	'把 in ba-const,"把"、"将"的词性标记'	,
// NN:	'Common nouns,普通名词',
// CC:	'Coordinating conjunction,并列连词，"和"	',
// NR:	'Proper nouns,专有名词',
// CD:	'Cardinal numbers,数字，"一百"	',
// NT:	'Temporal nouns,时序词，表示时间的名词',
// CS:	'Subordinating conj,从属连词（例子：若，如果，如…）	',
// OD:	'Ordinal numbers,序数词，"第一"',
// DEC:	'的 for relative-clause etc,"的"词性标记	',
// ON:	'Onomatopoeia,拟声词，"哈哈"',
// DEG:	'Associative,联结词"的"	',
// P:	'Preposition (excluding 把 and 被),介词',
// DER:	'in V-de construction, and V-de-R,"得"	',
// PN:	'pronouns,代词',
// DEV:	'before VP,地'	,
// PU:	'Punctuations,标点',
// DT:	'Determiner,限定词，"这"',
// SB:	'in long bei-construction,例子："被，给"',
// ETC:	'Tag for words, in coordination phrase,等，等等',
// SP:	'Sentence-final particle,句尾小品词，"吗"',
// FW:	'Foreign words,例子：ISO',
// VA:	'Predicative adjective,表语形容词，"红"',
// IJ:	'interjetion,感叹词'	,
// VC:	'Copula,系动词，"是"',
// JJ:	'Noun-modifier other than nouns'	,
// VE:	'有 as the main verb,"有"',
// LB:	'in long bei-construction,例子：被，给'	,
// VV:	'Other verbs,其他动词',
// LC:	'Localizer,定位词，例子："里"'	,

AD:	'副词',
M:	'量词',
AS:	'体态词',
MSP:'表示一些粒子',
BA:	'前置动词'	,
NN:	'普通名词',
CC:	'并列连词',
NR:	'专有名词',
CD:	'数字',
NT:	'时序词',
CS:	'从属连词',
OD:	'序数词',
DEC:	'词性标记	',
ON:	'拟声词',
DEG:	'联结词',
P:	'介词',
DER:	'表示动作的结果或状态',
PN:	'代词',
DEV:	'地'	,
PU:	'标点',
DT:	'限定词',
SB:	'长被动结构',
ETC:	'并列结构',
SP:	'句尾小品词',
FW:	'外来词',
VA:	'表语形容词',
IJ:	'感叹词'	,
VC:	'系动词',
JJ:	'名词修饰语'	,
VE:	'表示拥有',
LB:	'表示被动语态'	,
VV:	'其他动词',
LC:	'定位词'	,
}
const placeholder = ref('请输入内容')
baseService.get('/edge/tmodelcategory/allByType',{
  type: 'NLP'
}).then((res) => {
  console.log(res.data)
})
const handleSearch = () => {
  baseService.get('edge/predictdeploy/getAll').then(res => {
    getAll.value = res.data;
    console.log(res.data);
    value1.value = res.data[0].securityLevel
    value2.value = getValue(getLabel()[0])
    value3.value = getLabel()[0]
    // document.querySelector('.sf-label').innerHTML = getLabel()[0]
    // document.querySelector('.sf-label').style.color = '#303133'
})
baseService.get('edge/tmodelcategory/allByType',{
  type: 'NLP'
}).then(res => {
    options.value = res.data.list;
    console.log(res.data.list);

})
}

const submitContent = () => {
  if(value2.value === '' || textarea1.value === ''){
    ElMessage({
    message: '请选择模型和输入内容',
    type: 'warning',
  })
    return
  }
  let  result = ''
    if(value2.value === '/entity_recognize'){
      console.log('实体识别');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
  }).then((res) => {
      result = res.data.input_text
      for(let i = 0; i < res.data.entities.length; i++){
        // let entity = result.replace(res.data.entities[i].text,'['+res.data.entities[i].text+']:'+'['+entity_Recognize[res.data.entities[i].type]+']')
        let entity = result.replace(res.data.entities[i].text,`<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.entities[i].text}:${entity_Recognize[res.data.entities[i].type]}</span>`)
        result = entity
        
      }
      textarea2.value = result
      document.querySelector('.newT2').innerHTML = result
    }
  )}


    else if(value2.value === '/get_keyphrase'){
      console.log('关键词提取');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
  }).then((res) => {
      result = '关键词: '
      for(let i = 0; i < res.data.key_phrases.length; i++){
        // let entity = result.replace(res.data.key_phrases[i].span,'['+res.data.key_phrases[i].span+']:'+'[关键词]')
        let entity = `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.key_phrases[i].span}</span>`
        result += entity
      }
      //如果result为空，则说明没有提取到关键词
      if(result === ''){
        result = '没有提取到关键词'
      }
      textarea2.value = result
      document.querySelector('.newT2').innerHTML = result
    }
  )}
    else if(value2.value === '/part_of_speech'){
      console.log('词性标注');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
  }).then((res) => {
      result = ''
      console.log(res.data);
      for(let i = 0; i < res.data.pos_tagging.length; i++){
        // let entity = result.replace(res.data.pos_tagging[i].span,'['+res.data.pos_tagging[i].span+']:'+'['+pos_Tagging[`${res.data.pos_tagging[i].type}`]+']')
        
        let entity = `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.pos_tagging[i].span}:${pos_Tagging[`${res.data.pos_tagging[i].type}`]}</span>`
        result += entity + ' '
      }
      textarea2.value = result
      document.querySelector('.newT2').innerHTML = result
      
    }
  )}

    else if(value2.value === '/sentiment_analysis'){
      console.log('情感分析');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
  }).then((res) => {
      const sentiment ='消极度：' +`<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.negative_probability}</span>`  + "<br>" +'积极度：'+`<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${ res.data.positive_probability}</span>` +  "<br>" +'情绪：'+ `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.sentiment}</span>`
      textarea2.value = sentiment
      document.querySelector('.newT2').innerHTML = sentiment
    }
  )}
    else if(value2.value === '/summary_generation'){
      console.log('文本摘要');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
  }).then((res) => {
      textarea2.value = '文本摘要: '+ res.data.summary
      document.querySelector('.newT2').innerHTML = `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">文本摘要: </span>`+ res.data.summary
    }
  )}
    else if(value2.value === '/text_classification'){
      console.log('文本分类');
      axios.post(app.nlp_api + value2.value, {
    "text": textarea1.value,
    "labels": ["体育", "财经", "房产", "家居", "教育", "科技", "时尚", "时政", "游戏", "娱乐"],
      threshold: 0.5
  }).then((res) => {
    console.log(res.data.labels);
    for (let index = 0; index <res.data.labels.length; index++) {
        // textarea2.value = '文本分类: '+ res.data.labels[index].label + '，置信度：'+ res.data.labels[index].prob
        // document.querySelector('.newT2').innerHTML = '文本分类: '+ res.data.labels[index].label + '，置信度：'+ res.data.labels[index].prob
        result += '文本分类: '+ `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.labels[index].label}</span>` + '，置信度：'+ `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.labels[index].prob}</span>` + "<br>"
    }
    document.querySelector('.newT2').innerHTML = result
    }
  )}
    else if(value2.value === '/text_similarity'){
      console.log('文本相似性分析');
            //判断是否格式为text1-text2
            if(textarea1.value.split('-').length !== 2){
        ElMessage({
          message: '格式错误',
          type: 'warning',
        })
        return
      }
      axios.post(app.nlp_api + value2.value, {
        "text1": textarea1.value.split('-')[0],
        "text2": textarea1.value.split('-')[1],
  }).then((res) => {
      textarea2.value = '文本相似度: '+ res.data.similarity_score + "\n" + '是否相似: '+ res.data.label
      document.querySelector('.newT2').innerHTML = '文本相似度: '+ `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.similarity_score}</span>` + "<br>" + '是否相似: '+ `<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.label}</span>`
    }
    )}
    else if(value2.value === '/word_segmentation'){
      console.log('中文分词');
      axios.post(app.nlp_api + value2.value, {
      "text": textarea1.value,
      }).then((res) => {
        textarea2.value = '分词结果: '+ res.data.segmented_text.join('， ')
        for (let i = 0; i < res.data.segmented_text.length; i++) {
          result +=`<span style="background-color: #ccc;display:inline-block;padding: 0 2px;border-radius: 3px;color : #800080; font-size:18px; margin: 0 2px;">${res.data.segmented_text[i]}</span>` + ", "
          //如果是最后一个分词，则不加逗号
          
          if(i === res.data.segmented_text.length - 1){
            result = result.substring(0, result.length - 2)
            }
          }
        document.querySelector('.newT2').innerHTML = '分词结果: '+ result
        // document.querySelector('.newT2').innerHTML = '分词结果: '+ `<span style="background-color: yellow;">${res.data.segmented_text}</span>`
    }
  )}
  // axios.post(app.nlp_api + value.value, {"text": "巴黎奥"}, undefined)
  //   .then(response => {
  //     console.log('成功', response.data);
  //   })
  //   .catch(error => {
  //     console.error('失败', error);
  //   });
}
const handleSelectChange = (val) => {
  for(let i = 0; i < options.length; i++){
    if(options[i].label === val){
      value2.value = options[i].value
      // document.querySelector('.sf-label').innerHTML = val
      // document.querySelector('.sf-label').style.color = '#303133'
      
    }
  }
  if(val === '文本相似性分析'){
    placeholder.value = '请输入两个文本进行相似度分析，格式为：text1-text2，例如：我现在想睡觉-我现在想睡一会儿'
  }
  else{
    placeholder.value = '请输入内容'
  }

}
const handleSelectChange1 = (val) => {
  // value2.value = getValue()
}
onActivated(() => {
  handleSearch()
  value1.value = ''
  // document.querySelector('.sf-label').innerHTML = '请选择算法'
  // document.querySelector('.sf-label').style.color = '#c0bebe';
})
</script>

<style scoped>
.input-tip {
  padding-top: 10px;
  text-align: center;
  height: 40px;
  width: 80px;
  border-left: 3px solid rgba(24, 24, 221, 0.555);
}

.submit-btn {
  left: 99%;
  position: relative;
  z-index: 1;
  margin-top: 10px;
  margin-left: -76px;
}

.custom-word-limit {
  font-size: 12px;
  color: #909399;
  /* right: 220px; */
  position: relative;
  margin-right: 15px;
  margin-top: -25px;
}
.sf-label{
  padding-top: 10px;
  display: inline-block;
  text-align: center;
  position: relative;
  top: 1.5px;
  margin-left: 10px;
  height: 40px;
  width: 120px;
  border-radius: 5px;
  border: 1px solid rgb(224, 220, 225);
  color: #c0bebe;
}
.newT2{
  width: 100%;
  height: 120px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding:5px 10px;
}
</style>
