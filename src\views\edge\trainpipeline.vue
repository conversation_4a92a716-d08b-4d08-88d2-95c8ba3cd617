<template xmlns="http://www.w3.org/1999/html">
  <div class="mod-edge__trainpipeline">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addHandle()">新增</el-button>
      </el-form-item>
      <!--      <el-form-item>-->
      <!--        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>-->
      <!--      </el-form-item>-->
    </el-form>

    <el-row :gutter="20">
      <el-col :span="6" v-for="data in state.dataList" :key="data">
        <el-card shadow="hover" @click="() => onTrainSelect(data)" style="margin-top: 1em">
          <template #header>
            <div class="card-header">
              <span>训练名称: {{ data.modelName }}</span>
            </div>
          </template>
          <p class="text item">模型类型: {{ data.modelType }}</p>
          <p class="text item">节点ID: {{ data.nodeId }}</p>
        </el-card>
      </el-col>
    </el-row>

    <el-pagination :current-page="state.page" :page-sizes="[4, 8]" :page-size="state.limit"
                   :total="state.total" layout="total, sizes, prev, pager, next, jumper"
                   @size-change="state.pageSizeChangeHandle"
                   @current-change="state.pageCurrentChangeHandle"></el-pagination>


    <el-row :gutter="20" style="margin-top: 1em">
      <el-col :span="configInit ? 16 : 0">
        <trainpipeline-flow :onStageSelect="onStageSelect"></trainpipeline-flow>
      </el-col>
      <el-col :span="configInit ? 8 : 0">
        <el-card shadow="never" style="height: 100%">
          <template #header>
            <div class="card-header">
              <span v-if="selectedTrain.id">配置训练: {{ selectedTrain.modelName }}</span>
              <span v-else>新建训练</span>
            </div>
          </template>
          <!-- 新增 / 修改 -->
          <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList" :selectedStage="selectedStage">确定
          </add-or-update>
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import {reactive, ref, toRefs} from "vue";
import AddOrUpdate from "./trainpipeline-add-or-update.vue";
import TrainpipelineFlow from "@/views/edge/trainpipeline-flow.vue";
import {Stage, TrainTask} from "@/types/interface";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/trainmodel/page",
  getDataListIsPage: true,
  exportURL: "/edge/trainmodel/export",
  deleteURL: "/edge/trainmodel"
});

const state = reactive({...useView(view), ...toRefs(view)});
let emptyTrain = {} as TrainTask
const selectedTrain = ref(emptyTrain)
const selectedStage = ref<Stage>(undefined)
const configInit = ref(false) // 右侧配置框是否初始化

const addOrUpdateRef = ref();
const addHandle = () => {
  selectedTrain.value = {} as TrainTask
  addOrUpdateRef.value.init();
  configInit.value = true
};

const onTrainSelect = (train: TrainTask | any) => {
  selectedTrain.value = train
  addOrUpdateRef.value.init(train.id);
  configInit.value = true
}

const onStageSelect = (stage: Stage) => {
  selectedStage.value = stage
}
</script>
