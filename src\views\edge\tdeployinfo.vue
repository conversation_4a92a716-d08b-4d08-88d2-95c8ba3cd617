<template>
  <!-- 链路部署 -->
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.id" style="width: 200px" placeholder="id" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()"></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column prop="nodeId" :label="$t('edge.nodeID')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="linkId" :label="$t('edge.linkID')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="linkName" :label="$t('edge.linkName')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="linkStatus" :label="$t('edge.linkStatus')" header-align="center" align="center">
        <!-- 列标题上的提示框 -->
        <template v-slot:header>
          <el-tooltip effect="customized" :content="$t('edge.linkStatusContent')" placement="top" size="large">
            <span>{{ $t("edge.linkStatus") }}</span>
          </el-tooltip>
        </template>
        <!-- 使用新的 template 标签 -->
        <template #default="{ row }">
          <el-tag round :key="row.id" :type="getTagType(row.linkStatus)">
            {{ getLinkStatus(row.linkStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="configs" :label="$t('edge.parameterConfiguration')" header-align="center" align="center">
        <template #default="scope">
          <el-button @click="showConfigDataList(scope.row)" size="small">{{ $t("edge.showing") }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="modelPath" :label="$t('edge.modelDeploymentPath')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="memo" :label="$t('edge.remarks')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="200">
        <template #default="scope">
          <el-button size="small" bg type="success" text :disabled="isStartDisabled(scope.row)" @click="startModelAction(scope.row.id)">{{ $t("start") }} </el-button>
          <el-button size="small" bg type="primary" text :disabled="isStopDisabled(scope.row)" @click="stopModelAction(scope.row.id)">{{ $t("stop") }} </el-button>
          <el-button size="small" bg type="warning" text v-if="state.hasPermission('sys:user:update')" @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button size="small" bg type="danger" text v-if="state.hasPermission('sys:user:delete')" @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <show-data-list :dialogVisible="isShowDataList" :dataList="showLists" @close="closeDialogHandle1"></show-data-list>
  </div>
</template>

<script lang="ts" setup>
import { onActivated, reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./tdeployinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { TagType } from "@/types/interface";
import showDataList from "./show-data-list.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/tdeployinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tdeployinfo/export",
  deleteURL: "/edge/tdeployinfo",
  deleteIsBatch: true,
  dataForm: {
    id: ""
  }
});
const state = reactive({ ...useView(view), ...toRefs(view) });
const getTagType = (linkStatus: number): string => {
  const tagTypes: TagType = {
    1: "warning",
    2: "success",
    3: "success",
    4: "danger",
    5: "danger",
    99: "danger"
    // 添加更多状态的标签类型
  };
  return tagTypes[linkStatus] || "";
};
const getLinkStatus = (linkStatus: number): string => {
  let descriptions: TagType = {
    1: "部署中",
    2: "部署完成",
    3: "运行中",
    4: "节点停止",
    5: "节点丢失",
    99: "节点异常"
    // Add more descriptions as needed
  };
  return descriptions[linkStatus] || "";
};
const isStartDisabled = (rowData: any) => {
  return rowData.linkStatus === 3; // 如果 linkStatus 是 3，禁用启动按钮
};
onActivated(() => {
  state.getDataList()
});
const isStopDisabled = (rowData: any) => {
  return rowData.linkStatus === 4 || rowData.linkStatus === 5 || rowData.linkStatus === 99; // 如果 linkStatus 是 4 或者 5，禁用停止按钮
};
const isShowDataList = ref(false);
const showLists = ref({});
const showConfigDataList = (row: Object) => {
  isShowDataList.value = true;
  showLists.value = row;
};
const startModelAction = (id: any) => {
  let ControlCommandDTO = {
    id,
    action: "start",
    path: "",
    params: ""
  };
  state.dataListLoading = true;
  baseService.post(`/edge/tdeployinfo/controlCommand`, ControlCommandDTO).then(({ data: res }) => {
    if (res.code !== 0) {
      return ElMessage({
        type: "error",
        message: res.msg,
        duration: 2000
      });
    }
    Object.assign(state.dataForm, res.data);
  });
  setTimeout(() => {
    state.dataListLoading = false;
  }, 30000);
};
const stopModelAction = (id: any) => {
  let ControlCommandDTO = {
    id,
    action: "stop",
    path: "",
    params: ""
  };
  state.dataListLoading = true;
  baseService.post(`/edge/tdeployinfo/controlCommand`, ControlCommandDTO).then(({ data: res }) => {
    if (res.code !== 0) {
      return ElMessage({
        type: "error",
        message: res.msg,
        duration: 2000
      });
    }
    Object.assign(state.dataForm, res.data);
  });
  setTimeout(() => {
    state.dataListLoading = false;
  }, 30000);
};
const closeDialogHandle1 = () => {
  isShowDataList.value = false;
  showLists.value = {};
};
const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
</style>
