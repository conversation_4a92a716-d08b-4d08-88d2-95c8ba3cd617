<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="模型名称" prop="modelName">
        <el-input v-model="dataForm.modelName" placeholder="模型名称"></el-input>
      </el-form-item>
      <el-form-item label="模型文件名" prop="modelFileName">
        <el-input v-model="dataForm.modelFileName" placeholder="模型文件名"></el-input>
        <el-upload class="upload-demo" :action="url" :show-file-list="false" :on-success="handleModelFileSuccess">
          <el-button size="small" color="rgba(50,122,230,1)">上传模型</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="模型文件地址" prop="modelFileUrl">
        <el-input v-model="dataForm.modelFileUrl" placeholder="模型文件地址"></el-input>
      </el-form-item>
      <el-form-item label="参数配置" prop="configs">
        <el-input v-model="dataForm.configs" placeholder="参数配置"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input v-model="dataForm.memo" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";
import app from "@/constants/app";
import t from "@/i18n/zh-CN";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const url = ref(app.data_api + "/api/v1/upload");
const uploading = ref(false);

const dataForm = reactive({
  id: '',
  modelName: '',
  modelFileName: '',
  modelFileUrl: '',
  configs: '',
  memo: '',
  twfCreated: '',
  twfModified: '',
  twfDeleted: ''
});

const handleModelFileSuccess = (data: any) => {
  if (data && data.code === 0) {
    const currentHostname = window.location.hostname;
    // 获取当前端口号（port）
    const currentPort = window.location.port;
    const currentURL = window.location.protocol + "//" + currentHostname + (currentPort ? ":" + currentPort : "");
    dataForm.modelFileUrl = app.data_api + data.data.url;
    dataForm.modelFileName = data.data.fileName;
    uploading.value = false;
    ElMessage({
      type: "success",
      message: "文件上传成功!"
    });
  } else {
    ElMessage({
      type: "error",
      message: "文件上传失败!"
    });
  }
};

const rules = ref({
  modelName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelFileName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelFileUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
  // configs: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // memo: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfCreated: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfModified: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfDeleted: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/trainmodelinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/trainmodelinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
