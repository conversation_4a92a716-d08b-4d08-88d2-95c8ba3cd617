<template>
  <div class="mod-edge_workclothesinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border
              @selection-change="state.dataListSelectionChangeHandle" style="width: 100%" table-layout="auto">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="workClothesName" label="工服名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="workClothesType" label="工服类型" header-align="center" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
      <el-table-column label="图像" header-align="center" align="center">
        <template v-slot="scope" style="display: flex;">
          <el-space wrap>
            <div v-for="image in scope.row.imageInfo" :key="image.name"
                 style="width: fit-content;  display: inline ">
              <el-image style="height: 100px" :src="image.url" fit="contain"/>
            </div>
          </el-space>
        </template>
      </el-table-column>
      <!--              <el-table-column prop="created" label="创建时间" header-align="center" align="center"></el-table-column>s-->
      <!--              <el-table-column prop="modified" label="更新时间" header-align="center" align="censter"></el-table-column>-->
      <!--              <el-table-column prop="twfDeleted" label="删除时间" header-align="center" align="center"></el-table-column>-->
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="warning" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="danger" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit"
                   :total="state.total" layout="total, sizes, prev, pager, next, jumper"
                   @size-change="state.pageSizeChangeHandle"
                   @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import {reactive, ref, toRefs, watchEffect} from "vue";
import AddOrUpdate from "./work-clothes-add-or-update.vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/edge/workclothesinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/workclothesinfo/export",
  deleteURL: "/edge/workclothesinfo"
});

const state = reactive({...useView(view), ...toRefs(view)});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
watchEffect(() => {
  if (state.dataList == undefined) {
    return
  }
  for (const data of state.dataList) {
    let imageInfo = JSON.parse(data.imageInfo)
    if (Array.isArray(imageInfo)) {
      data.imageInfo = imageInfo
    } else {
      data.imageInfo = []
    }
  }
});
</script>
