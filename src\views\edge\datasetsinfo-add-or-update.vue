<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="数据集名称" prop="datasetsName">
        <el-input v-model="dataForm.datasetsName" placeholder="数据集名称"></el-input>
      </el-form-item>
      <el-form-item label="数据集文件名" prop="datasetsFileName">
        <el-input v-model="dataForm.datasetsFileName" placeholder="数据集文件名"></el-input>
        <el-upload class="upload-demo" :action="url" :auto-upload="false" method="put" ref="upload" :show-file-list="false" :on-change="handleModelFileSuccess">
          <el-button size="small" color="rgba(50,122,230,1)" >{{ $t("edge.datasetsName") }}</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="数据集大小" prop="datasetsSize">
        <el-input v-model="dataForm.datasetsSize" placeholder="数据集大小"></el-input>
      </el-form-item>
      <el-form-item label="数据集地址" prop="datasetsFileUrl">
        <el-input v-model="dataForm.datasetsFileUrl" placeholder="数据集地址"></el-input>
      </el-form-item>
      <el-form-item label="数据集类型(yolov8,yolov9)" prop="datasetsType">
        <el-input v-model="dataForm.datasetsType" placeholder="数据集类型 yolov8,yolov9"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="datasetsDec">
        <el-input v-model="dataForm.datasetsDec" placeholder="描述"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";
import app from "@/constants/app";
import { OssMetaData } from "@/types/interface";
import axios from "axios";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const url = ref(app.data_api + "/api/v1/upload");
// if (app.data_api === ""){
//     url.value = window.location.host+"/api/v1/upload"
// }
console.log(url);

const metaDataUrl = ref(app.api+'/oss/meta-data');
console.log(app.api);

if (app.api === ""){
    metaDataUrl.value = window.location.host+'/oss/meta-data'
}

console.log(metaDataUrl);

const uploading = ref(false);
let upload = ref<any>(null)
const dataForm = reactive({
  id: '',
  datasetsName: '',
  datasetsFileName: '',
  datasetsSize: '',
  datasetsFileUrl: '',
  datasetsType: '',
  datasetsDec: '',
  twfCreated: '',
  twfModified: '',
  twfDeleted: '',
  metaId: ''
});
function guid() {
    function S4() {
       return (((1+Math.random())*0x10000)|0).toString(16).substring(1);
    }
    return (S4()+S4()+"-"+S4()+"-"+S4()+"-"+S4()+"-"+S4()+S4()+S4());
}
const handleModelFileSuccess = async (file:any) => {
  console.log(file);
  file.name.toString()
  if (file.status == 'ready') {
    const newurl = await baseService.get('/oss/getUrl',{'bucketName':'dataset','objectName':file.name});
    // url.value = decodeURIComponent(newurl.data)
    url.value = encodeURI(newurl.data)
    console.log(url.value);
    upload.value.submit()
  }
  else if (file.status == 'success') {
    const downloadurl = await baseService.get('/oss/getdownloadUrl',{'bucketName':'dataset','objectName':file.name});
    // downurl.value = encodeURI(downloadurl.data)
    console.log(encodeURI(downloadurl.data));
    dataForm.datasetsFileUrl= encodeURI(downloadurl.data)
    dataForm.datasetsFileName = file.name;
    dataForm.datasetsSize = file.size
    uploading.value = false;
    ElMessage({
      type: "success",
      message: "文件上传成功!"
    });
      // 保存文件元数据到业务后端
  const metaData:OssMetaData = {
    fileId: file.uid,
    url: encodeURI(downloadurl.data),
    fileName: file.name + guid(),
    fileSize: file.size,
    fileType: 'dataset',
    bucketName: 'dataset',
    objectName: file.name,
    prefix: ''
  }
  
  baseService.post(metaDataUrl.value,metaData).then(res => {
    dataForm.metaId = res.data.id//这里是把得到的metaid赋值并且存放在datasetid，用以形成外键
  })

  // 调用 label studio api 从oss同步。 目前暂时写死，后续绑定到业务系统时可能移动至后端实现
  // const headers = {
  //   authorization: "Token 36f2bea6ee44c7399d30d2c32414a66d39e2a1db"
  //   // authorization: "Token 93a402fad24e8ad7e5a32a541c476cc7ff63cf30"
  // }
  // const syncUrl = "http://192.168.1.66:32656/api/storages/s3/1/sync"
  // // const syncUrl = "http://192.168.1.4:8080/api/storages/s3/1/sync"
  // axios.post(syncUrl, undefined, { headers })
  //   .then(response => {
  //     console.log('Label-Studio 同步成功', response.data);
  //   })
  //   .catch(error => {
  //     console.error('Label-Studio 同步失败', error);
  //   });

  } else {
    ElMessage({
      type: "error",
      message: "文件上传失败!"
    });
  }

};
const rules = ref({
  datasetsName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsFileName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  // datasetsSize: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  datasetsFileUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsType: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/datasetsinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/datasetsinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
