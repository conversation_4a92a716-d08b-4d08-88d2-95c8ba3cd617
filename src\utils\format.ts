export function formatTimestamp(timestamp: any) {
  // timestamp = Math.floor(timestamp / 1000);
  // 创建一个 Date 对象并将时间戳传递给它
  const date = new Date(+timestamp);
  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = ("0" + date.getHours()).slice(-2);
  const minutes = ("0" + date.getMinutes()).slice(-2);
  const seconds = ("0" + date.getSeconds()).slice(-2);
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedTime;
}

export function formatDate() {
  const nowTime = new Date();
  const year = nowTime.getFullYear();
  let month = (nowTime.getMonth() + 1).toString().padStart(2, "0");
  let day = nowTime.getDate().toString().padStart(2, "0");
  let hours = nowTime.getHours().toString().padStart(2, "0");
  let minutes = nowTime.getMinutes().toString().padStart(2, "0");
  let seconds = nowTime.getSeconds().toString().padStart(2, "0");
  const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedTime; // 输出指定格式的时间字符串
}
