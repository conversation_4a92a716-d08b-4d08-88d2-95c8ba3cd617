<template>
  <div class="gpu-container">
    <div class="header-controls">
      <h2>GPU 监控</h2>
      <el-select v-model="currentHostId" placeholder="选择主机" @change="handleHostChange" class="host-select">
        <el-option v-for="host in hostList" :key="host" :label="`主机 - ${getIp(host)}`" :value="host" />
      </el-select>
    </div>

    <div v-if="loading" class="loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      加载中...
    </div>

    <div v-else-if="gpuType === 'nvidia' && gpuInfo?.gpu?.length">
      <!-- 驱动和CUDA版本表格 -->
      <div class="driver-info">
        <table class="info-table">
          <thead>
            <tr>
              <th width="50%">驱动版本</th>
              <th>CUDA 版本</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ gpuInfo.driverVersion }}</td>
              <td>{{ gpuInfo.cudaVersion }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- GPU卡列表 -->
      <el-collapse accordion>
        <el-collapse-item v-for="(item, index) in gpuInfo.gpu" :key="index" :name="index">
          <template #title>
            <div class="collapse-title">
              <span>{{ index }}. {{ item.productName }}</span>
            </div>
          </template>

          <!-- 基础信息部分 -->
          <div class="section-title">基础信息</div>

          <table class="gpu-info-table">
            <thead>
              <tr>
                <th>GPU 使用率</th>
                <th>
                  温度
                  <el-tooltip content="GPU当前温度" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
                <th>
                  性能状态
                  <el-tooltip content="GPU性能状态" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
                <th>功率</th>
                <th>显存使用率</th>
                <th>风扇转速</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ item.gpuUtil }}</td>
                <td>{{ item.temperature }}</td>
                <td>{{ item.performanceState }}</td>
                <td>{{ item.powerDraw || "-" }} / {{ item.maxPowerLimit || "-" }}</td>
                <td>{{ item.memUsed }} / {{ item.memTotal }}</td>
                <td>{{ item.fanSpeed }}</td>
              </tr>
            </tbody>
          </table>

          <table class="gpu-info-table">
            <thead>
              <tr>
                <th>总线地址</th>
                <th>
                  持续模式
                  <el-tooltip content="持续模式状态" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
                <th>显示激活</th>
                <th>
                  Uncorr. ECC
                  <el-tooltip content="错误检查与纠正" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
                <th>
                  计算模式
                  <el-tooltip content="计算模式状态" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
                <th>
                  MIG M.
                  <el-tooltip content="多实例GPU模式" placement="top">
                    <el-icon>
                      <Info />
                    </el-icon>
                  </el-tooltip>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ item.busID }}</td>
                <td>{{ item.persistenceMode === "Enabled" ? "开启" : "关闭" }}</td>
                <td>{{ item.displayActive === "Enabled" ? "是" : "否" }}</td>
                <td>{{ item.ecc === "N/A" ? "不适用" : item.ecc === "Enabled" ? "开启" : "关闭" }}</td>
                <td>{{ item.computeMode === "Default" ? "默认" : item.computeMode }}</td>
                <td>{{ item.migMode === "N/A" ? "不支持" : item.migMode === "Enabled" ? "开启" : "关闭" }}</td>
              </tr>
            </tbody>
          </table>

          <!-- 进程信息部分 -->
          <div class="section-title">进程信息</div>

          <div v-if="item.processes && item.processes.length > 0">
            <table class="process-table">
              <thead>
                <tr>
                  <th>PID</th>
                  <th>类型</th>
                  <th>进程名称</th>
                  <th>显存使用</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="process in item.processes" :key="process.pid">
                  <td>{{ process.pid }}</td>
                  <td>{{ process.type }}</td>
                  <td>{{ process.processName }}</td>
                  <td>{{ process.usedMemory }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="no-process-data">暂无数据</div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <div v-else class="no-data-container">
      <img src="@/assets/images/no_app.svg" alt="No GPU data" />
      <div class="no-data-message">暂无GPU数据</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { getGpuLoad, os, safeApiCall } from "@/utils/api";
import { AI } from "@/interface/ai";
import { Loading } from "@element-plus/icons-vue";
import { InfoFilled as Info } from "@element-plus/icons-vue";

const loading = ref(false);
const gpuInfo = ref<AI.Info>({
  cudaVersion: "",
  driverVersion: "",
  type: "nvidia",
  gpu: []
});
const xpuInfo = ref<AI.XpuInfo>({
  driverVersion: "",
  type: "xpu",
  xpu: []
});
const gpuType = ref("nvidia");
// 当前主机ID
const currentHostId = ref<string>("");
const hostList = ref<string[]>([]);
const allData = ref<Record<string, any>>({});
const osDatas = ref<any>({});

// 获取路由信息
const route = useRoute();

// 尝试从路由查询参数获取初始 hostId
const initialHostIdFromRoute = route.query.hostId as string | undefined;
if (initialHostIdFromRoute) {
  currentHostId.value = initialHostIdFromRoute;
  // console.log('currentHostId', currentHostId.value);
}

// 获取GPU负载数据
const search = async () => {
  loading.value = true;
  try {
    // 并行获取 GPU 和 OS 数据
    const [gpuRes, osRes] = await Promise.all([
      safeApiCall(getGpuLoad),
      safeApiCall(os)
    ]);

    // 处理 OS 数据
    if (osRes?.data?.code === 0 && osRes.data.data) {
      osDatas.value = osRes.data.data;
    } else {
      console.error("获取 OS 数据失败或格式不正确", osRes);
      osDatas.value = {}; // 清空或设置默认值
    }

    // 处理 GPU 数据
    if (!gpuRes?.data || gpuRes.data.code !== 0 || !gpuRes.data.data) {
      console.error("GPU API响应格式不正确", gpuRes);
      hostList.value = [];
      currentHostId.value = "";
      gpuInfo.value = { cudaVersion: "", driverVersion: "", type: "nvidia", gpu: [] };
      xpuInfo.value = { driverVersion: "", type: "xpu", xpu: [] };
      return;
    }

    const responseData = gpuRes.data.data;
    allData.value = responseData; // 存储完整数据

    if (Object.keys(responseData).length === 0) {
      hostList.value = [];
      currentHostId.value = "";
      gpuInfo.value = { cudaVersion: "", driverVersion: "", type: "nvidia", gpu: [] };
      xpuInfo.value = { driverVersion: "", type: "xpu", xpu: [] };
      return;
    }

    hostList.value = Object.keys(responseData);

    // 检查 hostId 是否有效且存在于列表中
    if (currentHostId.value && !hostList.value.includes(currentHostId.value)) {
      console.warn(`路由参数中的 hostId "${currentHostId.value}" 无效或不在主机列表中，将选择第一个主机。`);
      currentHostId.value = ""; // 重置无效的 hostId
    }

    // 如果 currentHostId 仍然为空 (没有从路由获取或路由值无效)，则选择第一个主机
    if (hostList.value.length > 0 && !currentHostId.value) { 
      currentHostId.value = hostList.value[0]; // 默认选择第一个主机
    }

    updateGpuData();

  } catch (error) {
    console.error("处理API响应时出错:", error);
    hostList.value = [];
    currentHostId.value = "";
    gpuInfo.value = { cudaVersion: "", driverVersion: "", type: "nvidia", gpu: [] };
    xpuInfo.value = { driverVersion: "", type: "xpu", xpu: [] };
  } finally {
    loading.value = false;
  }
};

// 根据 hostId 获取 IP 地址
const getIp = (hostId: string): string => {
  if (osDatas.value && osDatas.value[hostId] && osDatas.value[hostId].ipv4Addr && osDatas.value[hostId].ipv4Addr !== 'IPNotFound') {
    return osDatas.value[hostId].ipv4Addr;
  }
  return hostId; // 如果找不到 IP，则返回 hostId
};

// 更新 GPU 数据的函数
const updateGpuData = () => {
  if (!currentHostId.value || !allData.value[currentHostId.value]) {
    gpuInfo.value = { cudaVersion: "", driverVersion: "", type: "nvidia", gpu: [] };
    xpuInfo.value = { driverVersion: "", type: "xpu", xpu: [] };
    gpuType.value = "nvidia"; // 或者设置为一个默认值或空
    return;
  }

  const actualData = allData.value[currentHostId.value];
  if (!actualData.type) {
    actualData.type = "nvidia";
  }
  gpuType.value = actualData.type;

  if (actualData.type === "nvidia") {
    gpuInfo.value = {
      cudaVersion: actualData.cudaVersion || "",
      driverVersion: actualData.driverVersion || "",
      type: "nvidia",
      gpu: Array.isArray(actualData.gpu) ? actualData.gpu : []
    };
    xpuInfo.value = { driverVersion: "", type: "xpu", xpu: [] }; // 清空 XPU 数据
  } else {
    xpuInfo.value = {
      driverVersion: actualData.driverVersion || "",
      type: "xpu",
      xpu: Array.isArray(actualData.xpu) ? actualData.xpu : []
    };
    gpuInfo.value = { cudaVersion: "", driverVersion: "", type: "nvidia", gpu: [] }; // 清空 Nvidia 数据
  }
}

// 处理主机切换
const handleHostChange = () => {
  updateGpuData(); // 使用已缓存的数据更新显示
};

onMounted(() => {
  search();
});
</script>

<style lang="scss" scoped>
.gpu-container {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #303133;
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
    font-size: 16px;

    .el-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }

  .driver-info {
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 14px;
    font-weight: 500;
    margin: 15px 0 10px 0;
  }

  .info-table,
  .gpu-info-table,
  .process-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;

    th,
    td {
      padding: 12px 8px;
      text-align: left;
      border: 1px solid #ebeef5;
    }

    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
      font-size: 14px;
    }

    td {
      color: #606266;
      font-size: 14px;
    }

    th .el-icon {
      margin-left: 4px;
      font-size: 14px;
      vertical-align: middle;
      color: #909399;
    }
  }

  .collapse-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .no-process-data {
    padding: 15px;
    color: #909399;
    text-align: center;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: #f8f8f8;
    border-radius: 4px;
    color: #909399;

    .no-data-message {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }

  :deep(.el-collapse-item__header) {
    font-size: 16px;
    padding: 0 8px;
  }

  :deep(.el-collapse-item__content) {
    padding: 15px 8px;
  }

  .header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin-bottom: 0; /* 移除 h2 的下边距 */
    }

    .host-select {
      width: 200px; /* 或根据需要调整宽度 */
    }
  }
}
</style>
