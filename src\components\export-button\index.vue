<template>
  <el-button type="primary" @click="handleExport">导出</el-button>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import app from '@/constants/app';

const props = defineProps({
  url: {
    type: String,
    required: true
  },
  query: {
    type: Object,
    default: () => ({})
  }
});

const handleExport = () => {
  try {
    // 构建查询参数字符串
    let queryString = '';
    if (props.query && Object.keys(props.query).length > 0) {
      queryString = '?' + Object.entries(props.query)
        .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
        .join('&');
    }
    
    // 使用window.open打开下载链接
    const fullUrl = `${app.api}${props.url}${queryString}`;
    window.open(fullUrl, '_blank');
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败', error);
    ElMessage.error('导出失败');
  }
};
</script>

<style scoped>
</style> 