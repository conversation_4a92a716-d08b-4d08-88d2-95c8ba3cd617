<template>
  <!-- 节点管理 -->
  <div class="mod-bga__monitoringstation">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.id" placeholder="id" clearable style="width: 200px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList()"></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50" class="myRedCheckBox"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center" width="150px"></el-table-column>
      <el-table-column prop="sname" label="节点名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="saddress" :label="$t('edge.nodeMacAddress')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="sinterface" :label="$t('edge.interfaceName')" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="snicSpeed" :label="$t('edge.networkCardSpeed')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="sv4Address" :label="$t('edge.ipv4Address')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="sv4Gateway" :label="$t('edge.ipv4Gateway')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="sv4Method" :label="$t('edge.ipv4Method')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="sv4Netmask" :label="$t('edge.ipv4SubnetMask')" header-align="center" align="center"></el-table-column> -->
      <el-table-column label="查看详情" header-align="center" align="center" width="150px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="details(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template #default="scope">
          <el-button type="warning" v-if="state.hasPermission('sys:user:update')" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="danger" link v-if="state.hasPermission('sys:user:delete')" @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>

  <el-dialog v-model="dialogTableVisible" title="节点详情" width="800" style="height: 400px; overflow: auto">
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100" style="height: 100px; text-align: center" v-for="item in gridData" :key="item.id">
      <tr>
        <td style="color: #327AE6; font-weight: bold">ID</td>
        <td>{{ item.id }}</td>
        <td style="color: #327AE6; font-weight: bold">节点名称</td>

        <td>{{ item.sname }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">节点地址</td>
        <td>{{ item.saddress }}</td>
        <td style="color: #327AE6; font-weight: bold">接口名称</td>
        <td>{{ item.sinterface }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">网卡速率</td>
        <td>{{ item.snicSpeed }}</td>
        <td style="color: #327AE6; font-weight: bold">IPv4地址</td>
        <td>{{ item.sv4Address }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">IPv4网关</td>
        <td>{{ item.sv4Gateway }}</td>

        <td style="color: #327AE6; font-weight: bold">IPv4方式</td>
        <td>{{ item.sv4Method }}</td>
      </tr>
      <tr>
        <td style="color: #327AE6; font-weight: bold">IPv4子网掩码</td>
        <td>{{ item.sv4Netmask }}</td>
      </tr>
    </table>

    <br />
  </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./tnodeinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { Delete, Edit, Search, Share, Upload } from "@element-plus/icons-vue";
const { $t } = globalLanguage();
const view = reactive({
  getDataListURL: "/edge/tnodeinfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tnodeinfo/export",
  deleteURL: "/edge/tnodeinfo",
  deleteIsBatch: true,
  dataForm: {
    id: ""
  }
});
//根据id获取详情
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const dialogTableVisible = ref(false);
const gridData = ref();
//查看详情
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : []; // 包装在数组中
  dialogTableVisible.value = true;
};
const state = reactive({ ...useView(view), ...toRefs(view) });
const addOrUpdateRef = ref();
// 修改
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
<style scoped>
/* 设置checkbox获得焦点后，对勾框的边框颜色 */

.el-checkbox__inner {
  background-color: #475665;
  border: none;
}
.el-checkbox__label {
  color: red;
}

.is-checked::after {
  background-color: red;
}
.is-focus::after {
  background-color: red;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: red;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: red;
}
  table {
  width: 100%; /* 设置表格宽度为100% */
  max-width: 1200px; /* 设置表格的最大宽度 */
  border-collapse: collapse; /* 使用合并边框 */
  table-layout: fixed; /* 固定布局 */
}
th,
td {
  padding: 10px; /* 单元格内边距 */
  overflow: auto; /* 溢出隐藏 */
  white-space: nowrap; /* 不换行 */
}
</style>
