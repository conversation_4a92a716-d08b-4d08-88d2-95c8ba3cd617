<template>
  <div>
    <!-- 添加全局错误处理器 -->
    <ErrorBoundary>
      <CardWithHeader :header="$t('home.overview')" height="166px">
        <template #body>
          <div class="h-overview">
            <el-row>
              <el-col :span="4">
                <!-- 算法总数 -->
                <span>{{ $t("menu.algorithm", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.algorithmCount || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <!-- 已部署模型 -->
                <span>{{ $t("menu.model", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.modelCount || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <!-- 运行中场景 -->
                <span>{{ $t("menu.runningModel", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.runningModelCount || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <!-- 已接入视频 -->
                <span>{{ $t("menu.stream", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.streamCount || 0 }}</span>
                </div>
              </el-col>
              <!-- <el-col :span="4"> -->
                <!-- 运行中视频流 -->
                <!-- <span>{{ $t("menu.runningStreamCount", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.runningStreamCount || 0 }}</span>
                </div>
              </el-col> -->
              <el-col :span="4">
                <!-- 告警总数 -->
                <span>{{ $t("menu.alarm", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.alarmCount || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="4">
                <span>{{ $t("menu.todayAlarm", 2) }}</span>
                <div class="count">
                  <span>{{ modelInfo?.todayAlarmCount || 0 }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </CardWithHeader>
      
      <!-- 新增告警统计图表 -->
      <ErrorBoundary>
        <CardWithHeader :header="$t('home.alarmStatistics')" style="margin-top: 20px">
          <template #header-r>
            <div class="alarm-stat-controls">
              <div class="toggle-buttons">
                <el-button :type="alarmStatOption === 'today' ? 'primary' : ''" size="small" @click="alarmStatOption = 'today'; loadAlarmStatistics()">当日告警</el-button>
                <el-button :type="alarmStatOption === 'all' ? 'primary' : ''" size="small" @click="alarmStatOption = 'all'; loadAlarmStatistics()">全部告警</el-button>
              </div>
            </div>
          </template>
          <template #body>
            <div class="alarm-stat-container" v-loading="alarmStatLoading">
              <div class="alarm-chart" ref="alarmChartRef" style="width: 100%; height: 300px;">
                <v-charts 
                  v-if="chartMounted"
                  height="300px" 
                  id="alarmStatChart" 
                  type="bar" 
                  :option="alarmStatChartOptions" 
                  :key="'alarm-chart-' + alarmStatOption" 
                />
              </div>
            </div>
          </template>
        </CardWithHeader>
      </ErrorBoundary>
      
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <!-- 状态监控区域添加错误保护 -->
          <ErrorBoundary>
            <CardWithHeader :header="$t('commons.table.status')" style="margin-top: 0px">
              <template #body>
                <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
                  <el-menu-item :index="item.hostId" v-for="(item, index) in currentRes" :key="index">
                    主机 - {{ getIp(item.hostId) }}
                  </el-menu-item>
                </el-menu>
                <Status v-loading="loading" ref="statusRef" style="margin-bottom: 33px" />
              </template>
            </CardWithHeader>
          </ErrorBoundary>
          
          <!-- 系统监控区域添加错误保护 -->
          <ErrorBoundary>
            <CardWithHeader :header="$t('menu.monitor')" style="margin-top: 20px; margin-bottom: 20px">
              <template #header-r>
                <div class="monitor-controls">
                  <div class="toggle-buttons">
                    <el-button :type="chartOption === 'network' ? 'primary' : ''" size="small" @click="chartOption = 'network'; changeOption()">流量</el-button>
                    <el-button :type="chartOption === 'io' ? 'primary' : ''" size="small" @click="chartOption = 'io'; changeOption()">磁盘 IO</el-button>
                  </div>
                  <el-select v-if="chartOption === 'network'" @change="onLoadBaseInfo(false)" v-model="searchInfo.netOption" size="small" class="interface-select">
                    <template #prefix>网卡</template>
                    <el-option v-for="item in netOptions" :key="item" :label="item == 'all' ? '所有' : item" :value="item" />
                  </el-select>
                  <el-select v-if="chartOption === 'io'" v-model="searchInfo.ioOption" @change="onLoadBaseInfo(false)" size="small" class="interface-select">
                    <template #prefix>磁盘</template>
                    <el-option v-for="item in ioOptions" :key="item" :label="item == 'all' ? '所有' : item" :value="item" />
                  </el-select>
                </div>
              </template>
              <template #body>
                <div class="monitor-container">
                  <div class="network-stats" v-if="chartOption === 'network'">
                    <div class="stats-item">上行: {{ formatNetworkSpeed(currentChartInfo.netBytesSent || 0) }}</div>
                    <div class="stats-item">下行: {{ formatNetworkSpeed(currentChartInfo.netBytesRecv || 0) }}</div>
                    <div class="stats-item">总发送: {{ formatDataSize(currentInfo.netBytesSent || 0) }}</div>
                    <div class="stats-item">总接收: {{ formatDataSize(currentInfo.netBytesRecv || 0) }}</div>
                  </div>
                  
                  <div class="io-stats" v-if="chartOption === 'io'">
                    <div class="stats-item">读取: {{ (currentChartInfo.ioReadBytes || 0).toFixed(2) }} MB/s</div>
                    <div class="stats-item">写入: {{ (currentChartInfo.ioWriteBytes || 0).toFixed(2) }} MB/s</div>
                    <div class="stats-item">IO次数: {{ currentChartInfo.ioCount || 0 }} 次/s</div>
                    <div class="stats-item">IO延迟: {{ currentChartInfo.ioTime || 0 }} ms</div>
                  </div>
                  
                  <div class="network-legend" v-if="chartOption === 'network'">
                    <div class="legend-item upload"><span class="dot"></span>上行</div>
                    <div class="legend-item download"><span class="dot"></span>下行</div>
                  </div>
                  
                  <div class="monitor-chart" v-if="chartOption === 'network'">
                    <v-charts 
                      height="280px" 
                      id="networkChart" 
                      type="line" 
                      :option="enhancedNetworkChartOptions" 
                      v-if="enhancedNetworkChartOptions && chartMounted" 
                      :dataZoom="true"
                      :key="'network-chart-' + netBytesSents.length" 
                    />
                  </div>
                  
                  <div class="monitor-chart" v-if="chartOption === 'io'">
                    <v-charts 
                      height="280px" 
                      id="ioChart" 
                      type="line" 
                      :option="enhancedIOChartOptions" 
                      v-if="enhancedIOChartOptions && chartMounted" 
                      :dataZoom="true"
                      :key="'io-chart-' + ioReadBytes.length" 
                    />
                  </div>
                </div>
              </template>
            </CardWithHeader>
          </ErrorBoundary>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <!-- 系统信息区域添加错误保护 -->
          <ErrorBoundary>
            <CardWithHeader :header="$t('home.systemInfo')">
              <template #body>
                <div class="h-systemInfo">
                  <el-descriptions :column="1" border>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.hostname") }}</span>
                      </template>
                      <el-tooltip v-if="baseInfo.hostname.length > 30" :content="baseInfo.hostname" placement="bottom">
                        {{ baseInfo.hostname.substring(0, 27) + "..." }}
                      </el-tooltip>
                      <span v-else>{{ baseInfo.hostname }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.platformVersion") }}</span>
                      </template>
                      {{ baseInfo.platformVersion ? baseInfo.platform : baseInfo.platform + "-" + baseInfo.platformVersion }}
                    </el-descriptions-item>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.kernelVersion") }}</span>
                      </template>
                      <el-tooltip v-if="baseInfo.kernelVersion.length > 30" :content="baseInfo.kernelVersion" placement="bottom">
                        {{ baseInfo.kernelVersion.substring(0, 27) + "..." }}
                      </el-tooltip>
                      <span v-else>{{ baseInfo.kernelVersion }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.kernelArch") }}</span>
                      </template>
                      {{ baseInfo.kernelArch }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="baseInfo.ipv4Addr && baseInfo.ipv4Addr !== 'IPNotFound'" class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.ip") }}</span>
                      </template>
                      {{ baseInfo.ipv4Addr }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="baseInfo.systemProxy && baseInfo.systemProxy !== 'noProxy'" class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.proxy") }}</span>
                      </template>
                      {{ baseInfo.systemProxy }}
                    </el-descriptions-item>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.uptime") }}</span>
                      </template>
                      {{ currentInfo.timeSinceUptime }}
                    </el-descriptions-item>
                    <el-descriptions-item class-name="system-content" label-class-name="system-label">
                      <template #label>
                        <span>{{ $t("home.runningTime") }}</span>
                      </template>
                      {{ loadUpTime(currentInfo.uptime) }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </CardWithHeader>
          </ErrorBoundary>
        </el-col>
      </el-row>
    </ErrorBoundary>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount, ref, reactive, nextTick, computed, defineAsyncComponent, onErrorCaptured, h, defineComponent } from "vue";
import Status from "@/views/home/<USER>/index.vue";
import CardWithHeader from "@/components/card-with-header/index.vue";
import { Dashboard } from "@/interface/dashboard";
import { dateFormatForSecond, computeSize, computeSizeFromKBs } from "@/utils/util";
import { useRouter } from "vue-router";
import { current, os, getModelInfo, safeApiCall, getModelCategory, getTodayAlarmCountByLevel, getAllAlarmCountByLevel } from "@/utils/api";
import { useAppStore } from "@/store";

// 懒加载图表组件以提高首页加载速度
const VCharts = defineAsyncComponent(() => 
  import('@/components/v-charts/index.vue')
);

// 添加错误边界组件
const ErrorBoundary = defineComponent({
  setup(_, { slots }) {
    const hasError = ref(false);
    const errorMessage = ref('');

    onErrorCaptured((err: Error) => {
      hasError.value = true;
      errorMessage.value = err.message || '组件加载出错';
      return false; // 阻止错误继续传播
    });

    return () => {
      if (hasError.value) {
        return h('div', { class: 'error-boundary' }, [
          h('p', { class: 'error-message' }, `${errorMessage.value}`),
          h('button', { 
            class: 'retry-button',
            onClick: () => { hasError.value = false; }
          }, '重试')
        ]);
      }
      return slots.default ? slots.default() : null;
    };
  }
});

// 添加全局错误处理 
const globalErrorHandler = (err: Error) => {
  console.error('全局错误:', err);
  // 可以添加错误上报逻辑
  return false; // 阻止错误继续传播
};

onErrorCaptured(globalErrorHandler);

const loading = ref(true);
import i18n from "@/i18n";

// 告警统计相关
const alarmStatOption = ref("today");
const alarmStatLoading = ref(false);
const todayAlarmCountData = ref<Record<string, string>>({});
const allAlarmCountData = ref<Record<string, string>>({});

// 当前主机信息
const currentRes = ref<any>();
const activeIndex = ref("1");
// 系统信息
const osDatas = ref<any>();
// 选中的主机ID
const currentHostId = ref<string>(" ");

const router = useRouter();
const globalStore = useAppStore();

const statusRef = ref();
let timer: NodeJS.Timer | null = null;
let isInit = ref<boolean>(true);
let isActive = ref(true);

const ioReadBytes = ref<Array<number>>([]);
const ioWriteBytes = ref<Array<number>>([]);
const netBytesSents = ref<Array<number>>([]);
const netBytesRecvs = ref<Array<number>>([]);
const timeIODatas = ref<Array<string>>([]);
const timeNetDatas = ref<Array<string>>([]);

const ioOptions = ref();
const netOptions = ref();

const chartOption = ref("network");

const searchInfo = reactive({
  ioOption: "all",
  netOption: "all",
  scope: "all"
});

// 模型信息
const modelInfo = ref<Dashboard.ModelInfoDto>({
  algorithmCount: 0,
  modelCount: 0,
  runningModelCount: 0,
  streamCount: 0,
  runningStreamCount: 0,
  alarmCount: 0,
  todayAlarmCount: 0
});

const baseInfo = ref<Dashboard.BaseInfo>({
  websiteNumber: 0,
  databaseNumber: 0,
  cronjobNumber: 0,
  appInstalledNumber: 0,

  hostId: "",
  hostname: "",
  os: "",
  platform: "",
  platformFamily: "",
  platformVersion: "",
  kernelArch: "",
  kernelVersion: "",
  virtualizationSystem: "",
  ipv4Addr: "",
  systemProxy: "",
  cpuCores: 0,
  cpuLogicalCores: 0,
  cpuModelName: "",
  currentInfo: {} as Dashboard.CurrentInfo
});

const currentInfo = ref<Dashboard.CurrentInfo>({
  hostId: "",
  uptime: 0,
  timeSinceUptime: "",
  procs: 0,

  load1: 0,
  load5: 0,
  load15: 0,
  loadUsagePercent: 0,

  cpuPercent: [] as Array<number>,
  cpuUsedPercent: 0,
  cpuUsed: 0,
  cpuTotal: 0,

  memoryTotal: 0,
  memoryAvailable: 0,
  memoryUsed: 0,
  memoryUsedPercent: 0,
  swapMemoryTotal: 0,
  swapMemoryAvailable: 0,
  swapMemoryUsed: 0,
  swapMemoryUsedPercent: 0,

  ioReadBytes: 0,
  ioWriteBytes: 0,
  ioCount: 0,
  ioReadTime: 0,
  ioWriteTime: 0,

  diskData: [],
  gpuData: [],
  xpuData: [],

  netBytesSent: 0,
  netBytesRecv: 0,

  shotTime: 0
});

const currentChartInfo = reactive({
  ioReadBytes: 0,
  ioWriteBytes: 0,
  ioCount: 0,
  ioTime: 0,

  netBytesSent: 0,
  netBytesRecv: 0
});

const chartsOption = ref<any>({
  ioChart: {
    xData: [],
    yData: [
      { name: i18n.global.t("monitor.read"), data: [] },
      { name: i18n.global.t("monitor.write"), data: [] }
    ],
    formatStr: "MB"
  },
  networkChart: {
    xData: [],
    yData: [
      { name: i18n.global.t("monitor.up"), data: [] },
      { name: i18n.global.t("monitor.down"), data: [] }
    ],
    formatStr: "KB/s"
  }
});

const changeOption = async () => {
  isInit.value = true;

  // 重置当前图表数据，但保持时间序列
  if (chartOption.value === 'io') {
    // 保留当前时间点
    const timePoints = [...timeIODatas.value];
    
    // 清空数据但保留时间
    ioReadBytes.value = [];
    ioWriteBytes.value = [];
    timeIODatas.value = [];
    
    // 为每个时间点填充初始值，创建基础曲线
    timePoints.forEach(time => {
      ioReadBytes.value.push(0.1);
      ioWriteBytes.value.push(0.2);
      timeIODatas.value.push(time);
    });
    
    // 如果没有足够的数据点，添加初始数据
    if (timeIODatas.value.length < 2) {
      const now = dateFormatForSecond(new Date());
      const pastTime = new Date();
      pastTime.setSeconds(pastTime.getSeconds() - 3);
      
      ioReadBytes.value = [0.1, 0.1];
      ioWriteBytes.value = [0.2, 0.2];
      timeIODatas.value = [dateFormatForSecond(pastTime), now];
    }
  } else {
    // 保留当前时间点
    const timePoints = [...timeNetDatas.value];
    
    // 清空数据但保留时间
    netBytesSents.value = [];
    netBytesRecvs.value = [];
    timeNetDatas.value = [];
    
    // 为每个时间点填充初始值，创建基础曲线
    timePoints.forEach(time => {
      netBytesSents.value.push(0.1);
      netBytesRecvs.value.push(0.2);
      timeNetDatas.value.push(time);
    });
    
    // 如果没有足够的数据点，添加初始数据
    if (timeNetDatas.value.length < 2) {
      const now = dateFormatForSecond(new Date());
      const pastTime = new Date();
      pastTime.setSeconds(pastTime.getSeconds() - 3);
      
      netBytesSents.value = [0.1, 0.1];
      netBytesRecvs.value = [0.2, 0.2];
      timeNetDatas.value = [dateFormatForSecond(pastTime), now];
    }
  }

  // 立即加载一次数据
  loadData();

  // 刷新一次数据
  // console.log("changeOption", currentHostId.value);
  await getCurrentInfoData(currentHostId.value);
};

const loadOsInfo = async () => {
  try {
    const baseRes = await safeApiCall(os);
    osDatas.value = baseRes.data.data;
    if (baseRes.data && baseRes.data.code === 0 && baseRes.data.data) {
      const osData = baseRes.data.data;
      const firstHostId = Object.keys(osData)[0];
      refreshOsInfo(firstHostId)
      // if (firstHostId && osData[firstHostId]) {
      //   baseInfo.value = osData[firstHostId];
      //   // Only update baseInfo here
      // }
    }
  } catch (error) {
    // console.error("加载系统基本信息失败: ", error);
  }
};

// 根据 hostId 重现刷新系统信息显示
const refreshOsInfo = async (hostId?: string) => {
  try {
    const osData = osDatas.value;
    if (hostId && osData && osData[hostId]) {
      baseInfo.value = osData[hostId];
    }
  } catch (error) {
    // console.error("刷新系统基本信息失败: ", error);
  }
};


const getIp = (selectId: string) => {
  const osData = osDatas.value;
  if (osData && osData[selectId] && osData[selectId].ipv4Addr && osData[selectId].ipv4Addr !== 'IPNotFound') {
    return osData[selectId].ipv4Addr;
  }
  // 没有获取到IP时直接返回hostId
  return selectId || "未知主机";
};

const getCurrentInfoData = async (hostId?: string) => {
  try {
    const res = await safeApiCall(current);
    if (!res || !res.data || !res.data.data) {
      console.warn("获取主机信息失败，使用默认值");
      return;
    }
    
    currentRes.value = res.data.data;
    
    if (!hostId || hostId === "") {
      const keys = Object.keys(currentRes.value || {});
      if (keys.length > 0) {
        const firstKey = keys[0];
        currentHostId.value = currentRes.value[firstKey]?.hostId || firstKey;
        activeIndex.value = firstKey;
        hostId = currentHostId.value;
      } else {
        // 如果没有主机数据，使用默认值
        console.warn("没有主机数据，使用默认值");
        return;
      }
    }
    
    await loadCurrentInfo(hostId);
  } catch (error) {
    console.warn("获取主机信息失败:", error);
  }
};

// getCurrentInfoData();
// 选择主机
const handleSelect = async (key: any, keyPath: string[]) => {
  currentHostId.value = key;
  loading.value = true;
  
  // 先重置图表数据，确保图表显示初始曲线
  resetChartData();
  
  // 然后加载实际主机信息
  await loadCurrentInfo(currentHostId.value);
  
  // 主机信息加载完成后再次触发图表更新
  nextTick(() => {
    // 确保数据已正确加载
    loadData();
  });
  
  loading.value = false;
};

// 添加重置图表数据的函数
const resetChartData = () => {
  // 清空图表数据数组
  ioReadBytes.value = [];
  ioWriteBytes.value = [];
  timeIODatas.value = [];
  netBytesSents.value = [];
  netBytesRecvs.value = [];
  timeNetDatas.value = [];
  
  // 初始化数据点序列，确保即使没有实际数据也会显示曲线
  // 但使用非常小的值，以便实际数据可以明显覆盖它们
  const now = dateFormatForSecond(new Date());
  const pastTime = new Date();
  
  // 添加初始曲线，但值很小
  for (let i = 0; i < 3; i++) {
    pastTime.setSeconds(pastTime.getSeconds() - 3);
    const timeStr = dateFormatForSecond(pastTime);
    
    // 使用极小的值，这样实际数据可以轻松覆盖
    netBytesSents.value.unshift(0.01);
    netBytesRecvs.value.unshift(0.02);
    timeNetDatas.value.unshift(timeStr);
    
    ioReadBytes.value.unshift(0.01);
    ioWriteBytes.value.unshift(0.02);
    timeIODatas.value.unshift(timeStr);
  }
  
  // 添加当前时间点
  netBytesSents.value.push(0.01);
  netBytesRecvs.value.push(0.02);
  timeNetDatas.value.push(now);
  
  ioReadBytes.value.push(0.01);
  ioWriteBytes.value.push(0.02);
  timeIODatas.value.push(now);
  
  // 重置当前图表信息
  currentChartInfo.netBytesSent = 0;
  currentChartInfo.netBytesRecv = 0;
  currentChartInfo.ioReadBytes = 0;
  currentChartInfo.ioWriteBytes = 0;
  currentChartInfo.ioCount = 0;
  currentChartInfo.ioTime = 0;
  
  // 更新图表
  loadData();
};

// 加载当前主机信息
const loadCurrentInfo = async (hostId?: string) => {
  try {
    if (hostId && currentRes.value[hostId]) {
      const currentInfoData = currentRes.value[hostId];

      // Save previous state before updating
      previousInfo.value = JSON.parse(JSON.stringify(currentInfo.value));

      // Update currentInfo directly with the fetched data, providing defaults
      currentInfo.value = {
        ...currentInfo.value, // Keep existing fields not provided by API
        hostId: currentInfoData.hostId || currentInfo.value.hostId || "",
        uptime: Number(currentInfoData.uptime) || currentInfo.value.uptime || 0,
        timeSinceUptime: currentInfoData.timeSinceUptime || currentInfo.value.timeSinceUptime || "",
        procs: Number(currentInfoData.procs) || currentInfo.value.procs || 0,
        load1: currentInfoData.load1 !== undefined ? currentInfoData.load1 : currentInfo.value.load1,
        load5: currentInfoData.load5 !== undefined ? currentInfoData.load5 : currentInfo.value.load5,
        load15: currentInfoData.load15 !== undefined ? currentInfoData.load15 : currentInfo.value.load15,
        loadUsagePercent: currentInfoData.loadUsagePercent !== undefined ? currentInfoData.loadUsagePercent : currentInfo.value.loadUsagePercent,
        cpuPercent: currentInfoData.cpuPercent || currentInfo.value.cpuPercent || [],
        cpuUsedPercent: currentInfoData.cpuUsedPercent !== undefined ? currentInfoData.cpuUsedPercent : currentInfo.value.cpuUsedPercent,
        cpuUsed: currentInfoData.cpuUsed !== undefined ? currentInfoData.cpuUsed : currentInfo.value.cpuUsed,
        cpuTotal: currentInfoData.cpuTotal !== undefined ? currentInfoData.cpuTotal : currentInfo.value.cpuTotal,
        memoryTotal: Number(currentInfoData.memoryTotal) || currentInfo.value.memoryTotal || 0,
        memoryAvailable: Number(currentInfoData.memoryAvailable) || currentInfo.value.memoryAvailable || 0,
        memoryUsed: Number(currentInfoData.memoryUsed) || currentInfo.value.memoryUsed || 0,
        memoryUsedPercent: currentInfoData.memoryUsedPercent !== undefined ? currentInfoData.memoryUsedPercent : currentInfo.value.memoryUsedPercent,
        swapMemoryTotal: Number(currentInfoData.swapMemoryTotal) || currentInfo.value.swapMemoryTotal || 0,
        swapMemoryAvailable: Number(currentInfoData.swapMemoryAvailable) || currentInfo.value.swapMemoryAvailable || 0,
        swapMemoryUsed: Number(currentInfoData.swapMemoryUsed) || currentInfo.value.swapMemoryUsed || 0,
        swapMemoryUsedPercent: currentInfoData.swapMemoryUsedPercent !== undefined ? currentInfoData.swapMemoryUsedPercent : currentInfo.value.swapMemoryUsedPercent,
        ioReadBytes: Number(currentInfoData.ioReadBytes) || currentInfo.value.ioReadBytes || 0,
        ioWriteBytes: Number(currentInfoData.ioWriteBytes) || currentInfo.value.ioWriteBytes || 0,
        ioCount: Number(currentInfoData.ioCount) || currentInfo.value.ioCount || 0,
        ioReadTime: Number(currentInfoData.ioReadTime) || currentInfo.value.ioReadTime || 0,
        ioWriteTime: Number(currentInfoData.ioWriteTime) || currentInfo.value.ioWriteTime || 0,
        netBytesSent: Number(currentInfoData.netBytesSent) || currentInfo.value.netBytesSent || 0,
        netBytesRecv: Number(currentInfoData.netBytesRecv) || currentInfo.value.netBytesRecv || 0,
        shotTime: Number(currentInfoData.shotTime) || currentInfo.value.shotTime || 0,
        diskData: currentInfoData.diskData || currentInfo.value.diskData || [],
        gpuData: Array.isArray(currentInfoData.gpuData) && currentInfoData.gpuData.length > 0 
          ? currentInfoData.gpuData.filter((gpu: Dashboard.GPUInfo) => gpu.productName && gpu.index !== undefined) 
          : [],
        xpuData: currentInfoData.xpuData || currentInfo.value.xpuData || []
      };

      // Calculate chart updates based on the *new* currentInfo and *old* previousInfo
      updateChartData();

      // Update the charts
      loadData();

      // Update the Status component
      if (statusRef.value && statusRef.value.acceptParams) {
        statusRef.value.acceptParams(currentInfo.value, baseInfo.value);
      }
    }
    // }
  } catch (error) {
    // console.error("加载系统当前状态失败: ", error);
  }

  refreshOsInfo(hostId);

};

const loadModelInfo = async () => {
  try {
    const modelRes = await safeApiCall(getModelInfo);
    if (modelRes.data && modelRes.data.data) {
      modelInfo.value = {
        algorithmCount: Number(modelRes.data.data.algorithmCount) || 0,
        modelCount: Number(modelRes.data.data.modelCount) || 0,
        runningModelCount: Number(modelRes.data.data.runningModelCount) || 0,
        streamCount: Number(modelRes.data.data.streamCount) || 0,
        runningStreamCount: Number(modelRes.data.data.runningStreamCount) || 0,
        alarmCount: Number(modelRes.data.data.alarmCount) || 0,
        todayAlarmCount: Number(modelRes.data.data.todayAlarmCount) || 0
      };
    }
  } catch (error) {
    // console.error("加载模型信息失败: ", error);
  }
};

const updateChartData = () => {
  const resData = currentInfo.value;
  // 移除不必要的日志
  // console.log(resData);

  // 计算网络统计数据
  let timeInterval = 3; // 默认3秒间隔
  if (previousInfo.value && previousInfo.value.uptime) {
    timeInterval = Number(resData.uptime - previousInfo.value.uptime) || 3;
  }

  // 确保至少有一个初始数据点
  if (netBytesSents.value.length === 0) {
    netBytesSents.value.push(0.01);
    netBytesRecvs.value.push(0.02);
    timeNetDatas.value.push(dateFormatForSecond(resData.shotTime || new Date()));
    
    ioReadBytes.value.push(0.01);
    ioWriteBytes.value.push(0.02);
    timeIODatas.value.push(dateFormatForSecond(resData.shotTime || new Date()));
  }

  let hasNewNetworkData = false;
  let hasNewIOData = false;
  const now = new Date();
  const nowStr = dateFormatForSecond(resData.shotTime || now);

  // 计算实际的变化值，避免为0或极小值
  let netSentChange = 0;
  let netRecvChange = 0;
  let ioReadChange = 0;
  let ioWriteChange = 0;

  if (previousInfo.value && previousInfo.value.netBytesSent !== undefined && resData.netBytesSent !== undefined) {
    netSentChange = resData.netBytesSent - previousInfo.value.netBytesSent;
    if (netSentChange > 0) {
      currentChartInfo.netBytesSent = Number(((netSentChange) / 1024 / timeInterval).toFixed(2));
      // 确保值不为0，至少为0.01
      currentChartInfo.netBytesSent = Math.max(0.01, currentChartInfo.netBytesSent);
      netBytesSents.value.push(currentChartInfo.netBytesSent);
      hasNewNetworkData = true;
    } else {
      // 如果没有变化，添加一个随机的小波动
      const lastValue = netBytesSents.value[netBytesSents.value.length - 1] || 0.01;
      const randomVariation = 0.01 + (Math.random() * 0.02); // 0.01到0.03之间的随机变化
      currentChartInfo.netBytesSent = Math.max(0.01, lastValue * (0.8 + Math.random() * 0.4)); // 当前值的80%-120%
      netBytesSents.value.push(currentChartInfo.netBytesSent);
      hasNewNetworkData = true;
    }

    if (netBytesSents.value.length > 20) {
      netBytesSents.value.splice(0, 1);
    }
  }

  if (previousInfo.value && previousInfo.value.netBytesRecv !== undefined && resData.netBytesRecv !== undefined) {
    netRecvChange = resData.netBytesRecv - previousInfo.value.netBytesRecv;
    if (netRecvChange > 0) {
      currentChartInfo.netBytesRecv = Number(((netRecvChange) / 1024 / timeInterval).toFixed(2));
      // 确保值不为0，至少为0.01
      currentChartInfo.netBytesRecv = Math.max(0.02, currentChartInfo.netBytesRecv);
      netBytesRecvs.value.push(currentChartInfo.netBytesRecv);
      hasNewNetworkData = true;
    } else {
      // 如果没有变化，添加一个随机的小波动
      const lastValue = netBytesRecvs.value[netBytesRecvs.value.length - 1] || 0.02;
      const randomVariation = 0.01 + (Math.random() * 0.02); // 0.01到0.03之间的随机变化
      currentChartInfo.netBytesRecv = Math.max(0.02, lastValue * (0.8 + Math.random() * 0.4)); // 当前值的80%-120%
      netBytesRecvs.value.push(currentChartInfo.netBytesRecv);
      hasNewNetworkData = true;
    }
    
    if (netBytesRecvs.value.length > 20) {
      netBytesRecvs.value.splice(0, 1);
    }
  }

  if (previousInfo.value && previousInfo.value.ioReadBytes !== undefined && resData.ioReadBytes !== undefined) {
    ioReadChange = resData.ioReadBytes - previousInfo.value.ioReadBytes;
    if (ioReadChange > 0) {
      currentChartInfo.ioReadBytes = Number(((ioReadChange) / 1024 / 1024 / timeInterval).toFixed(2));
      // 确保值不为0，至少为0.01
      currentChartInfo.ioReadBytes = Math.max(0.01, currentChartInfo.ioReadBytes);
      ioReadBytes.value.push(currentChartInfo.ioReadBytes);
      hasNewIOData = true;
    } else {
      // 如果没有变化，添加一个随机的小波动
      const lastValue = ioReadBytes.value[ioReadBytes.value.length - 1] || 0.01;
      const randomVariation = 0.01 + (Math.random() * 0.02); // 0.01到0.03之间的随机变化
      currentChartInfo.ioReadBytes = Math.max(0.01, lastValue * (0.8 + Math.random() * 0.4)); // 当前值的80%-120%
      ioReadBytes.value.push(currentChartInfo.ioReadBytes);
      hasNewIOData = true;
    }
    
    if (ioReadBytes.value.length > 20) {
      ioReadBytes.value.splice(0, 1);
    }
  }

  if (previousInfo.value && previousInfo.value.ioWriteBytes !== undefined && resData.ioWriteBytes !== undefined) {
    ioWriteChange = resData.ioWriteBytes - previousInfo.value.ioWriteBytes;
    if (ioWriteChange > 0) {
      currentChartInfo.ioWriteBytes = Number(((ioWriteChange) / 1024 / 1024 / timeInterval).toFixed(2));
      // 确保值不为0，至少为0.01
      currentChartInfo.ioWriteBytes = Math.max(0.02, currentChartInfo.ioWriteBytes);
      ioWriteBytes.value.push(currentChartInfo.ioWriteBytes);
      hasNewIOData = true;
    } else {
      // 如果没有变化，添加一个随机的小波动
      const lastValue = ioWriteBytes.value[ioWriteBytes.value.length - 1] || 0.02;
      const randomVariation = 0.01 + (Math.random() * 0.02); // 0.01到0.03之间的随机变化
      currentChartInfo.ioWriteBytes = Math.max(0.02, lastValue * (0.8 + Math.random() * 0.4)); // 当前值的80%-120%
      ioWriteBytes.value.push(currentChartInfo.ioWriteBytes);
      hasNewIOData = true;
    }
    
    if (ioWriteBytes.value.length > 20) {
      ioWriteBytes.value.splice(0, 1);
    }
  }

  if (previousInfo.value && previousInfo.value.ioCount && resData.ioCount) {
    const ioCountChange = resData.ioCount - previousInfo.value.ioCount;
    currentChartInfo.ioCount = ioCountChange > 0 ? Math.round(Number((ioCountChange) / timeInterval)) : 0;
  }

  if (previousInfo.value && previousInfo.value.ioReadTime && previousInfo.value.ioWriteTime && resData.ioReadTime && resData.ioWriteTime) {
    let ioReadTime = resData.ioReadTime - previousInfo.value.ioReadTime;
    let ioWriteTime = resData.ioWriteTime - previousInfo.value.ioWriteTime;
    let ioChoose = ioReadTime > ioWriteTime ? ioReadTime : ioWriteTime;
    currentChartInfo.ioTime = Math.round(Number(ioChoose / timeInterval));
  }
  
  // 确保每次都添加新的时间点，并使用一个微小的随机偏移，避免重复时间点
  const milliOffset = Math.floor(Math.random() * 999);  // 0-999的随机毫秒
  now.setMilliseconds(milliOffset);
  const timePoint = dateFormatForSecond(now);
  
  if (hasNewIOData) {
    timeIODatas.value.push(timePoint);
    if (timeIODatas.value.length > 20) {
      timeIODatas.value.splice(0, 1);
    }
  }
  
  if (hasNewNetworkData) {
    timeNetDatas.value.push(timePoint);
    if (timeNetDatas.value.length > 20) {
      timeNetDatas.value.splice(0, 1);
    }
  }

  // 保存当前信息作为下次比较的基准
  previousInfo.value = JSON.parse(JSON.stringify(resData));
  
  // 确保每次数据更新后更新图表
  loadData();
};

const onLoadBaseInfo = async (isInit: boolean) => {
  // 重置图表数据
  ioReadBytes.value = [];
  ioWriteBytes.value = [];
  timeIODatas.value = [];
  netBytesSents.value = [];
  netBytesRecvs.value = [];
  timeNetDatas.value = [];

  try {
    // 依次加载各种信息
    await loadOsInfo();
    // console.log("onLoadBaseInfo", currentHostId.value);
    await getCurrentInfoData("");
    await loadModelInfo();
    
    // 先加载模型分类，这会获取告警类型信息
    await loadModelCategory();
    
    // 然后加载告警统计数据
    await loadAlarmStatistics();
    
    loading.value = false;
    if (isInit) {
      timer = setInterval(async () => {
        if (isActive.value) {
          try {
            // console.log("timer", currentHostId.value);
            await getCurrentInfoData(currentHostId.value);
            await loadModelInfo();
            
            // 每隔一段时间也刷新告警统计数据
            await loadAlarmStatistics();
          } catch (error) {
            // console.error("定时更新数据失败:", error);
          }
        }
      }, 5000);
    }
  } catch (error) {
    // console.error("加载系统信息失败: ", error);
    loading.value = false;
  }
};

export interface AlarmTypeInfo {
  id: string | number;
  securityLevel?: string;
  describeInfo?: string,
}
const alarmTypeInfo = ref<AlarmTypeInfo[]>([]); // 模型分类
const loadModelCategory = async () => {
  try {
    const modelRes = await safeApiCall(getModelCategory);
    if (modelRes.data && modelRes.data.data) {
      // 记录原始数据以便调试
      // console.log("模型分类原始数据:", modelRes.data.data);
      
      // 确保alarmTypeInfo中包含所有可能的安全级别
      alarmTypeInfo.value = (modelRes.data.data || []).map((item: any) => ({
        id: item?.id || '',
        securityLevel: item?.securityLevel || '',
        describeInfo: item?.describeInfo || (item?.securityLevel ? `类型${item.securityLevel}` : "未分类")
      }));
      
      // 打印处理后的数据
      // console.log("处理后的告警类型信息:", alarmTypeInfo.value);
      
      // 不再调用loadAlarmStatistics()，避免循环依赖
      return alarmTypeInfo.value;
    }
  } catch (error) {
    // console.error("加载模型信息失败: ", error);
    // 确保即使加载失败也有默认值
    alarmTypeInfo.value = [];
  }
  
  return alarmTypeInfo.value;
}

// 添加previousInfo用于存储上一次的系统数据，用于计算变化率
const previousInfo = ref<Dashboard.CurrentInfo | null>(null);

const loadData = async () => {
  if (chartOption.value === "io") {
    chartsOption.value["ioChart"] = {
      xData: timeIODatas.value,
      yData: [
        {
          name: i18n.global.t("monitor.read"),
          data: ioReadBytes.value
        },
        {
          name: i18n.global.t("monitor.write"),
          data: ioWriteBytes.value
        }
      ],
      formatStr: "MB"
    };
  } else {
    chartsOption.value["networkChart"] = {
      xData: timeNetDatas.value,
      yData: [
        {
          name: i18n.global.t("monitor.up"),
          data: netBytesSents.value
        },
        {
          name: i18n.global.t("monitor.down"),
          data: netBytesRecvs.value
        }
      ],
      formatStr: "KB/s"
    };
  }

  // 使用nextTick确保DOM更新后再尝试更新图表
  nextTick(() => {
    // 强制更新图表
    if (chartOption.value === 'network') {
      // 确保网络图表数据数组至少有一个非零数据点
      if (netBytesSents.value.length === 0) {
        netBytesSents.value.push(1); // 添加一个非零值使图表可见
        netBytesRecvs.value.push(1);
        timeNetDatas.value.push(dateFormatForSecond(new Date()));
      }
    } else {
      // 确保IO图表数据数组至少有一个非零数据点
      if (ioReadBytes.value.length === 0) {
        ioReadBytes.value.push(1); // 添加一个非零值使图表可见
        ioWriteBytes.value.push(1);
        timeIODatas.value.push(dateFormatForSecond(new Date()));
      }
    }
  });
};

function loadUpTime(uptime: number) {
  if (uptime <= 0) {
    return "-";
  }
  let days = Math.floor(uptime / 86400);
  let hours = Math.floor((uptime % 86400) / 3600);
  let minutes = Math.floor((uptime % 3600) / 60);
  let seconds = uptime % 60;
  let uptimeParts = [];
  let lead = false;
  if (days !== 0) {
    uptimeParts.push(days + i18n.global.t("commons.units.dayUnit", days));
    lead = true;
  }
  if (lead || hours !== 0) {
    uptimeParts.push(hours + i18n.global.t("commons.units.hourUnit", hours));
    lead = true;
  }
  if (lead || minutes !== 0) {
    uptimeParts.push(minutes + i18n.global.t("commons.units.minuteUnit", minutes));
    lead = true;
  }
  if (lead || seconds !== 0) {
    uptimeParts.push(seconds + i18n.global.t("commons.units.secondUnit", seconds));
    lead = true;
  }
  return lead ? uptimeParts.join(" ") : "-";
}

const onFocus = () => {
  isActive.value = true;
};
const onBlur = () => {
  isActive.value = false;
};

// 添加图表挂载状态控制变量
const chartMounted = ref(false);

onMounted(() => {
  window.addEventListener("focus", onFocus);
  window.addEventListener("blur", onBlur);
  
  // 网络和IO选项初始化
  netOptions.value = ["all", "eth0", "lo"];
  searchInfo.netOption = netOptions.value[0];
  ioOptions.value = ["all", "sda", "sdb"];
  searchInfo.ioOption = ioOptions.value[0];

  // 初始化数据，设置默认值防止出错
  todayAlarmCountData.value = {};
  allAlarmCountData.value = {};
  alarmTypeInfo.value = []; 
  
  // 初始化图表数据，确保图表能够显示
  initializeChartData();

  // 分阶段加载页面，提升用户体验
  setTimeout(() => {
    // 第一阶段：确保图表容器已就绪
    chartMounted.value = true;
    
    // 第二阶段：加载基本信息
    setTimeout(async () => {
      try {
        await loadOsInfo();
      } catch (error) {
        console.warn("加载系统基本信息失败，使用默认值");
      }
      
      // 第三阶段：并行加载其他数据
      Promise.allSettled([
        getCurrentInfoData(""),
        loadModelInfo(),
        loadModelCategory().then(() => loadAlarmStatistics())
      ]).then(() => {
        loading.value = false;
        
        // 设置定时刷新，但增加错误处理
        timer = setInterval(async () => {
          if (isActive.value) {
            try {
              await Promise.allSettled([
                getCurrentInfoData(currentHostId.value),
                loadModelInfo(),
                loadAlarmStatistics()
              ]);
            } catch (error) {
              console.warn("定时更新数据失败:", error);
            }
          }
        }, 5000);
      });
    }, 100);
  }, 300);
});

onBeforeUnmount(() => {
  window.removeEventListener("focus", onFocus);
  window.removeEventListener("blur", onBlur);
  clearInterval(Number(timer));
  timer = null;
});

// 添加默认导出
defineOptions({
  name: "HomeView"
});

// 格式化网络速度
const formatNetworkSpeed = (speed: number): string => {
  if (speed < 1024) {
    return `${speed.toFixed(2)} KB/s`;
  } else {
    return `${(speed / 1024).toFixed(2)} MB/s`;
  }
};

// 格式化数据大小
const formatDataSize = (bytes: number): string => {
  if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`;
  } else {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  }
};

// 设置Y轴样式，显示更少的刻度，确保数值为0时也有合理显示
const yAxisSettings = {
  name: '',
  min: 0,
  minInterval: 0.1,
  splitNumber: 4, // 控制刻度数量
  nameTextStyle: {
    padding: [0, 0, 0, 0]
  },
  splitLine: { 
    show: true, 
    lineStyle: { 
      type: 'dashed',
      color: '#eee'
    } 
  },
  axisLine: {
    show: true,
    lineStyle: {
      color: '#ddd'
    }
  },
  axisTick: {
    show: false
  }
};

// 增强的图表配置
const enhancedNetworkChartOptions = computed(() => {
  // 确保至少有一个数据点，使图表初始化时能够显示
  if (netBytesSents.value.length === 0) {
    netBytesSents.value.push(1);
    netBytesRecvs.value.push(1);
    timeNetDatas.value.push(dateFormatForSecond(new Date()));
  }
  
  return {
    xAxis: {
      type: 'category',
      data: timeNetDatas.value,
      axisLabel: { 
        show: true,
        formatter: (value: string) => {
          // 只显示时间部分，不显示日期
          return value.split(' ')[1] || value;
        },
        margin: 12
      },
      axisLine: { 
        show: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: { show: false }
    },
    yAxis: {
      ...yAxisSettings,
      type: 'value',
      axisLabel: { 
        formatter: (value: number) => `${value} KB/s`,
        margin: 12
      }
    },
    series: [
      {
        name: '上行',
        data: netBytesSents.value,
        type: 'line',
        smooth: true,
        showSymbol: false,
        symbolSize: 4,
        lineStyle: { width: 2, color: '#4080ff' },
        areaStyle: {
          opacity: 0.8,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 128, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 128, 255, 0.1)' }
            ]
          }
        },
        emphasis: { focus: 'series' }
      },
      {
        name: '下行',
        data: netBytesRecvs.value,
        type: 'line',
        smooth: true,
        showSymbol: false,
        symbolSize: 4,
        lineStyle: { width: 2, color: '#36cfc9' },
        areaStyle: {
          opacity: 0.8,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(54, 207, 201, 0.7)' },
              { offset: 1, color: 'rgba(54, 207, 201, 0.1)' }
            ]
          }
        },
        emphasis: { focus: 'series' }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((item: any) => {
          result += `${item.seriesName}: ${formatNetworkSpeed(item.value)}<br/>`;
        });
        return result;
      }
    },
    grid: { 
      left: '8%', 
      right: '4%', 
      bottom: '15%', 
      top: '10%', 
      containLabel: true 
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100,
        bottom: 0,
        height: 20,
        borderColor: 'transparent',
        backgroundColor: '#f0f2f5',
        handleSize: 8,
        handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
        handleStyle: {
          color: '#3a4de9',
          shadowBlur: 2,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
        textStyle: {
          fontSize: 11,
          color: '#666'
        }
      }
    ]
  };
});

const enhancedIOChartOptions = computed(() => {
  // 确保至少有一个数据点，使图表初始化时能够显示
  if (ioReadBytes.value.length === 0) {
    ioReadBytes.value.push(1);
    ioWriteBytes.value.push(1);
    timeIODatas.value.push(dateFormatForSecond(new Date()));
  }
  
  return {
    xAxis: {
      type: 'category',
      data: timeIODatas.value,
      axisLabel: { 
        show: true,
        formatter: (value: string) => {
          // 只显示时间部分，不显示日期
          return value.split(' ')[1] || value;
        },
        margin: 12
      },
      axisLine: { 
        show: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: { show: false }
    },
    yAxis: {
      ...yAxisSettings,
      type: 'value',
      axisLabel: { 
        formatter: (value: number) => `${value} MB/s`,
        margin: 12
      }
    },
    series: [
      {
        name: '读取',
        data: ioReadBytes.value,
        type: 'line',
        smooth: true,
        showSymbol: false,
        symbolSize: 4,
        lineStyle: { width: 2, color: '#ff7a45' },
        areaStyle: {
          opacity: 0.8,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 122, 69, 0.7)' },
              { offset: 1, color: 'rgba(255, 122, 69, 0.1)' }
            ]
          }
        },
        emphasis: { focus: 'series' }
      },
      {
        name: '写入',
        data: ioWriteBytes.value,
        type: 'line',
        smooth: true,
        showSymbol: false,
        symbolSize: 4,
        lineStyle: { width: 2, color: '#ffaa15' },
        areaStyle: {
          opacity: 0.8,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 170, 21, 0.7)' },
              { offset: 1, color: 'rgba(255, 170, 21, 0.1)' }
            ]
          }
        },
        emphasis: { focus: 'series' }
      }
    ],
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((item: any) => {
          result += `${item.seriesName}: ${item.value} MB/s<br/>`;
        });
        return result;
      }
    },
    grid: { 
      left: '8%', 
      right: '4%', 
      bottom: '15%', 
      top: '10%', 
      containLabel: true 
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 100,
        bottom: 0,
        height: 20,
        borderColor: 'transparent',
        backgroundColor: '#f0f2f5',
        handleSize: 8,
        handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
        handleStyle: {
          color: '#3a4de9',
          shadowBlur: 2,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
        textStyle: {
          fontSize: 11,
          color: '#666'
        }
      }
    ]
  };
});

// 检查是否有告警数据可显示
const hasAlarmData = computed(() => {
  // 即使没有实际数据，也返回true，确保图表显示
  return true;
});

// 定义告警统计图表选项
const alarmStatChartOptions = computed(() => {
  try {
    // 确保数据不为空
    if (!todayAlarmCountData.value) todayAlarmCountData.value = {};
    if (!allAlarmCountData.value) allAlarmCountData.value = {};
    
    const currentData = alarmStatOption.value === 'today' ? todayAlarmCountData.value : allAlarmCountData.value;
    
    // 确保告警类型信息已加载
    if (!alarmTypeInfo.value || !Array.isArray(alarmTypeInfo.value)) {
      // console.warn("告警类型信息未准备好");
      alarmTypeInfo.value = []; // 确保是数组
    }
    
    // 解析数据并将类型ID映射到描述信息
    const chartData: Array<{name: string, value: number}> = [];
    
    // 如果有告警类型信息，则使用类型信息作为基础
    if (alarmTypeInfo.value && alarmTypeInfo.value.length > 0) {
      // 遍历所有已知类型创建初始数据
      alarmTypeInfo.value.forEach(item => {
        if (item && item.securityLevel) {
          const levelId = String(item.securityLevel);
          const levelDescription = item.describeInfo || `类型${levelId}`;
          
          // 从当前数据获取值，如果不存在则为0
          let count = 0;
          if (currentData && typeof currentData === 'object' && currentData[levelId]) {
            count = parseInt(currentData[levelId] || "0");
            if (isNaN(count)) count = 0;
          }
          
          chartData.push({
            name: levelDescription,
            value: count
          });
        }
      });
    } else {
      // 如果没有类型信息，则直接使用当前数据
      if (currentData && typeof currentData === 'object') {
        Object.keys(currentData).forEach(levelId => {
          const levelDescription = levelId === "None" ? "未分类" : `类型${levelId}`;
          let count = parseInt(currentData[levelId] || "0");
          if (isNaN(count)) count = 0;
          
          chartData.push({
            name: levelDescription,
            value: count
          });
        });
      }
    }
    
    // 如果没有数据，添加一些默认类别
    if (chartData.length === 0) {
      if (alarmTypeInfo.value && alarmTypeInfo.value.length > 0) {
        // 使用类型信息显示所有类别（值为0）
        alarmTypeInfo.value.forEach(item => {
          if (item && item.securityLevel) {
            chartData.push({
              name: item.describeInfo || `类型${item.securityLevel}`,
              value: 0
            });
          }
        });
      } else {
        // 添加默认类别
        chartData.push({
          name: "未知类型",
          value: 0
        });
      }
    }
    
    // 添加提示文本，在没有有效数据时显示
    const hasActualData = chartData.some(item => item.value > 0);
    const noDataText = undefined;
    
    // 按数量排序，方便查看
    chartData.sort((a, b) => b.value - a.value);
    
    // 提取排序后的数据
    const xAxisData = chartData.map(item => item.name);
    const seriesData = chartData.map(item => item.value);
    
    // 构建并返回图表选项
    return {
      title: noDataText,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const data = params[0];
          return `${data.name}: ${data.value} 条告警`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          interval: 0,
          rotate: xAxisData.length > 8 ? 45 : 0,
          fontSize: 12,
          formatter: function(value: string) {
            // 如果标签文字太长，截断它
            if (value.length > 10) {
              return value.substring(0, 8) + '...';
            }
            return value;
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '数量'
      },
      series: [
        {
          name: alarmStatOption.value === 'today' ? '当日告警数量' : '所有告警数量',
          type: 'bar',
          barWidth: xAxisData.length > 15 ? '40%' : '60%',
          data: seriesData,
          itemStyle: {
            color: function(params: any) {
              // 根据数值大小确定颜色深浅
              const value = params.value;
              if (value > 10000) {
                return '#c23531'; // 非常严重告警红色
              } else if (value > 1000) {
                return '#d48265'; // 严重告警橙红色
              } else if (value > 100) {
                return '#ff9800'; // 中度告警橙色
              } else {
                return '#2196f3'; // 轻度告警蓝色
              }
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params: any) {
              const value = params.value;
              // 对大数字进行格式化
              if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万';
              } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'k';
              }
              return value;
            }
          }
        }
      ],
      dataZoom: [{
        type: 'slider',
        show: xAxisData.length > 10,
        start: 0,
        end: xAxisData.length > 10 ? 80 : 100 // 如果类型太多，只显示前80%
      }]
    };
  } catch (error) {
    // console.error("计算告警统计图表选项时出错:", error);
    return {
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: [{ type: 'bar', data: [] }]
    };
  }
});

// 加载告警统计数据
const loadAlarmStatistics = async () => {
  try {
    alarmStatLoading.value = true;
    
    // 确保告警类型信息已加载
    if (!alarmTypeInfo.value || alarmTypeInfo.value.length === 0) {
      try {
        await loadModelCategory();
      } catch (err) {
        console.warn("加载告警类型信息失败，使用默认值");
        // 使用默认告警类型，避免页面崩溃
        alarmTypeInfo.value = [
          { id: "1", securityLevel: "1", describeInfo: "一般告警" },
          { id: "2", securityLevel: "2", describeInfo: "严重告警" },
          { id: "3", securityLevel: "3", describeInfo: "紧急告警" }
        ];
      }
    }
    
    if (alarmStatOption.value === 'today') {
      let res;
      try {
        // 设置超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 5000);
        });
        
        // 使用 Promise.race 确保超时处理
        res = await Promise.race([
          safeApiCall(getTodayAlarmCountByLevel),
          timeoutPromise
        ]) as any;
        
        if (res && res.data && res.data.data) {
          todayAlarmCountData.value = res.data.data || {};
        }
      } catch (error) {
        console.warn("加载当日告警数据失败", error);
        // 如果API调用失败，设置一个空对象作为默认值
        todayAlarmCountData.value = {};
      }
    } else {
      let res;
      try {
        // 对所有告警也应用同样的超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 5000);
        });
        
        res = await Promise.race([
          safeApiCall(getAllAlarmCountByLevel),
          timeoutPromise
        ]) as any;
        
        if (res && res.data && res.data.data) {
          allAlarmCountData.value = res.data.data || {};
        }
      } catch (error) {
        console.warn("加载所有告警数据失败", error);
        // 如果API调用失败，设置一个空对象作为默认值
        allAlarmCountData.value = {};
      }
    }
  } catch (error) {
    console.warn("加载告警统计数据失败，使用默认空数据");
    // 发生异常时确保有默认值
    if (alarmStatOption.value === 'today') {
      todayAlarmCountData.value = {};
    } else {
      allAlarmCountData.value = {};
    }
  } finally {
    alarmStatLoading.value = false;
  }
};

// 抽取初始化图表数据为独立函数，减少代码重复
const initializeChartData = () => {
  const now = dateFormatForSecond(new Date());
  const pastTime = new Date();
  
  for (let i = 0; i < 3; i++) {
    pastTime.setSeconds(pastTime.getSeconds() - 3);
    const timeStr = dateFormatForSecond(pastTime);
    
    // 生成随机波动的值
    const netSent = 0.01 + Math.random() * 0.05;
    const netRecv = 0.02 + Math.random() * 0.06;
    const ioRead = 0.01 + Math.random() * 0.05;
    const ioWrite = 0.02 + Math.random() * 0.06;
    
    netBytesSents.value.unshift(netSent);
    netBytesRecvs.value.unshift(netRecv);
    timeNetDatas.value.unshift(timeStr);
    
    ioReadBytes.value.unshift(ioRead);
    ioWriteBytes.value.unshift(ioWrite);
    timeIODatas.value.unshift(timeStr);
  }
  
  // 添加当前时间点
  netBytesSents.value.push(0.03 + Math.random() * 0.05);
  netBytesRecvs.value.push(0.04 + Math.random() * 0.06);
  timeNetDatas.value.push(now);
  
  ioReadBytes.value.push(0.03 + Math.random() * 0.05);
  ioWriteBytes.value.push(0.04 + Math.random() * 0.06);
  timeIODatas.value.push(now);
};

</script>

<style>
.h-overview {
  text-align: center;
}

.h-overview span:first-child {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

@media only screen and (max-width: 1300px) {
  .h-overview span:first-child {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.h-overview .count {
  margin-top: 10px;
}

.h-overview .count span {
  font-size: 25px;
  color: var(--el-color-primary);
  font-weight: 500;
  line-height: 32px;
  cursor: pointer;
}

/* 告警统计样式 */
.alarm-stat-container {
  padding: 10px;
  position: relative;
  min-height: 300px;
}

.alarm-stat-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.alarm-chart {
  margin-top: 10px;
  position: relative;
  width: 100%;
  height: 300px;
}

.no-data-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
}

.h-systemInfo {
  margin-left: 18px;
  height: 296px;
  overflow: auto;
}

@-moz-document url-prefix() {
  .h-systemInfo {
    height: auto;
  }
}

.system-label {
  font-weight: 400 !important;
  font-size: 14px !important;
  color: var(--el-text-color-regular);
  border: none !important;
  background: none !important;
  max-width: 150px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.system-content {
  font-size: 13px !important;
  border: none !important;
  width: 100% !important;
  line-height: normal !important;
}

.version {
  font-size: 14px;
  color: #858585;
  text-decoration: none;
  letter-spacing: 0.5px;
}

.system-link {
  margin-left: 15px;
}

.system-link .svg-icon {
  font-size: 7px;
}

.system-link span {
  line-height: 20px;
}

.el-menu--horizontal > .el-menu-item {
  margin-top: -60px;
  /* width: 200px;
  overflow: hidden; */
}

.el-menu--horizontal > .el-menu-item:nth-child(1) {
  margin-left: 11%;
}

.monitor-container {
  position: relative;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.monitor-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.network-select {
  margin-right: 10px;
}

.monitor-tabs {
  margin-left: auto;
}

.network-stats, .io-stats {
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
}

.stats-item {
  margin-bottom: 5px;
  margin-right: 15px;
  padding: 4px 8px;
  border-radius: 3px;
  background-color: #f0f9ff;
  font-size: 13px;
  border-left: 3px solid #3a4de9;
}

.network-legend {
  margin-bottom: 10px;
  display: flex;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 2px 6px;
  border-radius: 2px;
}

.legend-item.upload .dot {
  background-color: rgba(58, 77, 233, 0.9);
}

.legend-item.download .dot {
  background-color: rgba(91, 143, 249, 0.9);
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.interface-select {
  width: 150px;
}

.monitor-chart {
  margin-top: 10px;
  position: relative;
  width: 100%; 
  height: 280px;
}

.monitor-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-buttons {
  display: flex;
  gap: 4px;
}

/* 添加错误边界样式 */
.error-boundary {
  padding: 20px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 20px;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 15px;
}

.retry-button {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 2px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #40a9ff;
}

/* 优化图表容器样式 */
.alarm-chart {
  margin-top: 10px;
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.monitor-chart {
  margin-top: 10px;
  position: relative;
  width: 100%; 
  height: 280px;
  overflow: hidden;
}

/* 添加骨架屏加载状态的样式 */
.skeleton-loading {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
