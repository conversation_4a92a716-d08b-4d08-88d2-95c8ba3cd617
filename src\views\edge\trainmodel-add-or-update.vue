<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="模型训练名称" prop="modelName">
        <el-input v-model="dataForm.modelName" placeholder="模型名称"></el-input>
      </el-form-item>
      <el-form-item label="模型类型(yolov8,yolov9)" prop="modelType">
        <el-input v-model="dataForm.modelType" placeholder="模型类型 1: yolov8 2: yolov9 2: padda"></el-input>
      </el-form-item>
      <el-form-item label="基础模型名称" prop="modelBaseName">
        <!--        <el-input v-model="dataForm.modelBaseName" placeholder="基础模型名称"></el-input>-->
        <el-select class="m-2" v-model="dataForm.modelBaseName" value-key="id" @change="onSelectModel">
          <el-option v-for="item in modelInfo" :key="item.id" :label="item.modelName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="基础模型url 地址" prop="modelBaseUrl">
        <el-input v-model="dataForm.modelBaseUrl" placeholder="基础模型url 地址"></el-input>
      </el-form-item>
      <el-form-item label="数据集名称" prop="datasetsName">
        <!--        <el-input v-model="dataForm.datasetsName" placeholder="数据集名称"></el-input>-->
        <el-select class="m-2" v-model="dataForm.datasetsName" value-key="id" @change="onSelectData">
          <el-option v-for="item in dataInfo" :key="item.id" :label="item.datasetsName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="数据集URL" prop="datasetsUrl">
        <el-input v-model="dataForm.datasetsUrl" placeholder="数据集URL"></el-input>
      </el-form-item>
      <el-form-item label="执行命令行" prop="cmdTrain">
        <el-input v-model="dataForm.cmdTrain" placeholder="执行命令行"></el-input>
      </el-form-item>
      <el-form-item label="参数配置" prop="configs">
        <el-input v-model="dataForm.configs" placeholder="参数配置"></el-input>
      </el-form-item>
      <el-form-item label="训练后的模型" prop="configs">
        <el-input v-model="dataForm.modelAfterTrainingUrl" placeholder="参数配置"></el-input>
      </el-form-item>
      <el-form-item :label="'节点ID'" prop="nodeId">
        <el-select class="m-2" v-model="dataForm.nodeId" value-key="id" :placeholder="$t('edge.selectModel')"
                   size="large">
          <el-option v-for="item in nodesInfo" :key="item.id" :label="item.saddress" :value="item.saddress"/>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input v-model="dataForm.memo" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";
import {DatasetInfo, Modelinfo, NodesOrLinks, TrainModelInfo} from "@/types/interface";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',
  modelName: '',
  modelType: '',
  modelBaseName: '',
  modelBaseUrl: '',
  datasetsId: '',
  datasetsName: '',
  datasetsUrl: '',
  cmdTrain: '',
  nodeId: '',
  configs: '',
  modelAfterTrainingUrl: '',
  memo: '',
});

const rules = ref({
  modelName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelType: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelBaseName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelBaseUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  datasetsUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
  // cmdTrain: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // configs: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // memo: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfCreated: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfModified: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  // twfDeleted: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ]
});

const dataInfo = ref<DatasetInfo[]>([]); // 数据集列表
const modelInfo = ref<TrainModelInfo[]>([]); // 模型列表
const nodesInfo = ref<NodesOrLinks[]>([]);

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  getAllInfo();
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/datasetsinfo/page"), //加载摄像头列表
    baseService.get("/edge/trainmodelinfo/page"), //加载模型列表
    baseService.get("/edge/tnodeinfo/page"), // 加载node列表
  ]).then(([datainfo, modelinfo, nodeInfo]) => {
    dataInfo.value = datainfo.data.list;
    modelInfo.value = modelinfo.data.list;
    nodesInfo.value = nodeInfo.data.list;
  });
};

// 选择model
const onSelectModel = (value: any) => {
  // console.log(value);
  let it = modelInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.modelName) {
    dataForm.modelBaseName = it.modelFileName ?? '';
    dataForm.modelBaseUrl = it.modelFileUrl ?? '';
  }
};
// 选择数据集
const onSelectData = (value: any) => {
  let it = dataInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.datasetsName) {
    dataForm.datasetsId = value;
    dataForm.datasetsName = it.datasetsName ?? '';
    dataForm.datasetsUrl = it.datasetsFileUrl ?? '';
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/trainmodel/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/trainmodel", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
