export interface home{
        id: string,
        twfCreated: string,
        twfModified: string,
        twfDeleted: null,
        nodeId: string,
        bufferram: string,
        cpuload1: 0,
        cpuload15: 0,
        cpuload5: 0,
        cpunums: string,
        freeram: string,
        sharedram: string,
        temp: string,
        totaldisk: string,
        totalram: string,
        uptime: string,
        usedisk: string,
        tops: string,
}
export class homeData{
    datalist:home={
        id: "",
        twfCreated: "",
        twfModified: "",
        twfDeleted: null,
        nodeId: "",
        bufferram: "",
        cpuload1: 0,
        cpuload15: 0,
        cpuload5: 0,
        cpunums: "",
        freeram: "",
        sharedram: "",
        temp: "",
        totaldisk: "",
        totalram: "",
        uptime: "",
        usedisk: "",
        tops: "",
    }
}