<template>
    <div class="v-charts" ref="chartRef"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
    option: {
        type: Object,
        required: false,
        default: () => ({})
    },
    width: {
        type: String,
        default: '100%'
    },
    height: {
        type: String,
        default: '300px'
    }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const initChart = () => {
    if (!chartRef.value) return;
    if (chart) {
        chart.dispose();
        chart = null;
    }
    try {
        chart = echarts.init(chartRef.value);
        if (props.option && typeof props.option === 'object' && Object.keys(props.option).length > 0) {
            chart.setOption(props.option);
        }
    } catch (error) {
        console.error('初始化图表失败：', error);
    }
}

watch(() => props.option, (newVal) => {
    if (chart && newVal && typeof newVal === 'object' && Object.keys(newVal).length > 0) {
        try {
            chart.setOption(newVal, true);
        } catch (error: any) {
            const message = (error instanceof Error) ? error.message : String(error);
            if (message && message.includes('Instance') && message.includes('disposed')) {
                console.warn('尝试在已销毁的图表实例上更新选项，可能需要重新初始化:', error);
            } else {
                console.error('更新图表失败：', error);
            }
        }
    } else if (chart) {
        // Optionally clear the chart if the new option is invalid/empty
        // chart.clear();
    }
}, { deep: true })

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
        chart = null
    }
    window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

defineOptions({
  name: 'VCharts'
});
</script>

<style lang="scss" scoped>
.v-charts {
    width: v-bind(width);
    height: v-bind(height);
}
</style>
