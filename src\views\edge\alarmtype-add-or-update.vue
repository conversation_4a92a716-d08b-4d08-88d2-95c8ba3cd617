<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="模型类型代码" prop="securityLevel">
        <el-input v-model="dataForm.securityLevel" placeholder="预警类型"></el-input>
      </el-form-item>
      <el-form-item label="模型描述" prop="describeInfo">
        <el-input v-model="dataForm.describeInfo" placeholder="预警类型描述"></el-input>
      </el-form-item>
      <el-form-item label="模型类别" prop="describeInfo">
        <el-select v-model="dataForm.type" placeholder="预警类型描述">
          <el-option label="CV" value="CV"/>
          <el-option label="NLP" value="NLP"/>
          <el-option label="MLLM" value="MLLM"/>
          <el-option label="LLM" value="LLM"/>
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref, watchEffect} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";

export interface AlarmTypeInfo {
  id: string | number;
  securityLevel?: string;
  describeInfo?: string,
}

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '', securityLevel: '', describeInfo: '', type: '',
});
const alarmTypeInfo = ref<AlarmTypeInfo[]>([]);

const rules = ref({
  securityLevel: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  describeInfo: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  type: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  getAlarmTypeInfo()

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/tmodelcategory/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/tmodelcategory", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 获取预警类型清单
const getAlarmTypeInfo = () => {
  Promise.all([
    baseService.get("/edge/tmodelcategory/page"), //预警类型
  ]).then(([alarmTypeRes]) => {
    alarmTypeInfo.value = alarmTypeRes.data.list;
  });
};

defineExpose({
  init
});
</script>
