<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
             label-width="120px">
      <el-form-item label="镜像名" prop="trainName">
        <el-select class="m-2" v-model="dataForm.trainName" value-key="id" @change="onSelectImages" placeholder="选择镜像">
          <el-option v-for="item in imageInfo" :key="item.id" :label="item.imgName" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="模型名" prop="model">
        <el-input v-model="dataForm.model" placeholder="模型名"></el-input>
      </el-form-item>
      <el-form-item label="模型url" prop="modelAfterTrainingUrl">
        <el-input v-model="dataForm.modelAfterTrainingUrl" placeholder="镜像模型url"></el-input>
      </el-form-item>
      <el-form-item label="节点ID" prop="nodeId">
<!--        <el-input v-model="dataForm.nodeId" placeholder="节点ID"></el-input>-->
        <el-select class="m-2" v-model="dataForm.nodeId" value-key="id" :placeholder="$t('edge.selectModel')" size="large">
          <el-option v-for="item in nodesInfo" :key="item.id" :label="item.sname" :value="item.saddress"/>
        </el-select>
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <!-- <el-input v-model="dataForm.nodeName" placeholder="节点名称"></el-input> -->
        <el-select class="m-2" v-model="dataForm.nodeName" value-key="id" :placeholder="$t('edge.selectModel')" size="large">
          <el-option v-for="item in nodesInfo" :key="item.id" :label="item.sname" :value="item.sname"/>
        </el-select>

      </el-form-item>

      <el-form-item label="数据推送URL" prop="sendUrl">
        <el-select v-model="dataForm.sname" class="m-2" value-key="id" :placeholder="$t('edge.selectPush')" size="large"
                   @change="onSelectPush">
          <el-option v-for="item in pushInfo" :key="item.id" :label="item.sname" :value="item.id"/>
        </el-select>
        <el-input v-model="dataForm.sendUrl" placeholder="数据推送URL"></el-input>
      </el-form-item>

      <el-form-item label="视频输入URL" prop="videoInput">
        <el-select class="m-2" v-model="dataForm.videoName" value-key="id" :placeholder="$t('edge.selectModel')"
                   @change="onSelectCamera">
          <el-option v-for="item in cameraInfo" :key="item.id" :label="item.name" :value="item.uuid"/>
        </el-select>
        <el-input v-model="dataForm.videoInput" placeholder="视频输入URL"></el-input>
      </el-form-item>
      <el-form-item label="模型输出流地址" prop="outputStream">
        <el-input v-model="dataForm.outputStream" placeholder="模型输出流地址"></el-input>
      </el-form-item>

      <el-form-item label="安全预警模型" prop="securityLevel">
        <el-select class="m-2" v-model="dataForm.securityLevel" value-key="id" placeholder="请选择安全预警模型">
          <el-option v-for="item in alarmTypeInfo" :key="item.id" :label="item.describeInfo" :value="item.securityLevel"/>
        </el-select>
      </el-form-item>

      <el-popover
        placement="bottom-start"
        title=""

        trigger="click"
        :popper-style="popoverStyles"
      >
        <template #reference>
          <el-button class="m-2s">更多</el-button>
        </template>

                <el-form-item label="阈值" prop="threshold">
                  <el-input v-model="dataForm.threshold" placeholder="阈值"></el-input>
                </el-form-item>
                <el-form-item label="时间间隔 10 帧" prop="timeInter">
                  <el-input v-model="dataForm.timeInter" placeholder="时间间隔 10 帧"></el-input>
                </el-form-item>
                <el-form-item label="分析帧30" prop="analysisFrame">
                  <el-input v-model="dataForm.analysisFrame" placeholder="分析帧30"></el-input>
                </el-form-item>
                <el-form-item label="设备ID" prop="deviceId">
                  <el-input v-model="dataForm.deviceId" placeholder="设备ID"></el-input>
                </el-form-item>
                <el-form-item label="摄像头ID" prop="cameraId">
                  <el-input v-model="dataForm.cameraId" placeholder="摄像头ID"></el-input>
                </el-form-item>
                <el-form-item label="摄像头位置信息" prop="cameraLocal">
                  <el-input v-model="dataForm.cameraLocal" placeholder="摄像头位置信息"></el-input>
                </el-form-item>
                <el-form-item label="感兴趣区域坐标" prop="roi">
                  <el-input v-model="dataForm.roi" placeholder="感兴趣区域坐标"></el-input>
                </el-form-item>
                </el-popover>

    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";
import {Camerainfo, ImagesInfo, Modelinfo, NodesOrLinks, PushInfo, TrainInfo, TrainModelInfo} from "@/types/interface";
import {AlarmTypeInfo} from "@/views/edge/alarmtype-add-or-update.vue";
import { log } from "console";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',
  trainId: '',
  trainName: '',
  modelAfterTrainingUrl: '',
  nodeId: '',
  nodeName: '',
  model: '',
  sendUrl: '',
  sname: '',
  videoInput: '',//rtsp://admin:a18002368235@************:554/stream1&channel=1
  videoName: '',
  outputStream: '',// rtmp://*************:1935/live/camera
  securityLevel: '',
  threshold: '0.6',
  timeInter: '10',
  analysisFrame: '30',
  deviceId: '',
  cameraId: '',
  cameraLocal: '[\'xzu\',\'91.18577\',\'29.64932\']',
  roi: '[220,500,750,500,710,710,180,710]',
  containerId: '',
  predictPath: '',
  // twfCreated: '',
  // twfModified: '',
  // twfDeleted: ''
});

const rules = ref({
  trainId: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  trainName: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  modelAfterTrainingUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  nodeId: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  // nodeName: [
  //   {required: true, message: '必填项不能为空', trigger: 'blur'}
  // ],
  model: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  sendUrl: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ],
  videoInput: [
    {required: true, message: '必填项不能为空', trigger: 'blur'}
  ]

});

// const trainInfo = ref<TrainInfo[]>([]); // 训练列表
const imageInfo = ref<ImagesInfo[]>([]); // 训练列表
const nodesInfo = ref<NodesOrLinks[]>([]);  // 节点
const cameraInfo = ref<Camerainfo[]>([]); // 摄像头列表
const pushInfo = ref<PushInfo[]>([]); // 推送列表
const alarmTypeInfo = ref<AlarmTypeInfo[]>([]); // 推送列表

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  dataForm.sname = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  getAllInfo();

  if (id) {
    getInfo(id);
  }
};

// 获取全部信息
const getAllInfo = () => {
  Promise.all([
    baseService.get("/edge/imagesinfo/AllInfoList"), //加载镜像列表
    // baseService.get("/edge/tnodeinfo/page"), // 加载node列表
    baseService.get("/edge/dashboardsnapshot/page"), // 加载node列表
    baseService.get("/edge/tcamerainfo/AllInfoList"), //加载摄像头列表
    baseService.get("/edge/tpushinfo/page"), //加载推送列表
    baseService.get("/edge/tmodelcategory/allByType",{type:"CV"}), //加载预警列表
  ]).then(([ images, nodeInfo, tcamerainfo, tpushinfo, alarmTypeRes]) => {
    imageInfo.value = images.data;
    nodesInfo.value = nodeInfo.data.list;
    cameraInfo.value = tcamerainfo.data;
    pushInfo.value = tpushinfo.data.list;
    alarmTypeInfo.value = alarmTypeRes.data.list;
  });
};

// 选择镜像
const onSelectImages = (value: any) => {
  let it = imageInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.imgName) {
    dataForm.modelAfterTrainingUrl = it.imgUrl ?? '';
    const filename = extractFilename(dataForm.modelAfterTrainingUrl);
    dataForm.model = filename ?? '';
    dataForm.trainId = it.id.toString();
    dataForm.trainName = it.imgName ?? '';
  }
};

// 选择model
// const onSelectTrain = (value: any) => {
//   let it = trainInfo.value.find((item) => {
//     return (item.id == value);
//   });
//   if (it?.modelName) {
//     dataForm.modelAfterTrainingUrl = it.modelAfterTrainingUrl ?? '';
//     const filename = extractFilename(dataForm.modelAfterTrainingUrl);
//     dataForm.model = filename ?? '';
//     dataForm.trainId = it.id.toString();
//     dataForm.trainName = it.modelName ?? '';
//   }
// };

// 选择推送地址
const onSelectPush = (value: any) => {
  let it = pushInfo.value.find((item) => {
    return (item.id == value);
  });
  if (it?.sprotoName) {
    dataForm.sendUrl = it.sprotoName || '';
  }
};

// 选择摄像头
const onSelectCamera = (value: any) => {
  let it = cameraInfo.value.find((item) => {
    return (item.uuid == value);
  });
  if (it?.name) {
    dataForm.videoInput = it.streamUrl || '';
    dataForm.videoName = it.name;
    console.log(it);

    dataForm.deviceId = it.name || '';
    dataForm.cameraId = it.camId || '';

  }
};
//-------------------------------------------------------------------------------------------------

function extractFilename(url: string): string {
  // Split the URL by '/'
  const parts = url.split('/');
  // Return the last part which is the filename
  return parts[parts.length - 1];
}

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/edge/predictdeploy/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/edge/predictdeploy", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
const popoverStyles =  {
        backgroundColor: '#f5f5f5',
        width: '45vw'
      }
</script>
<style scoped>
.el-button + .el-button {
  margin-left: 8px;
}
.m-2s{
  margin-left: 20px;
}
</style>
