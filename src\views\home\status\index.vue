<template>
  <div class="h-status">
    <el-row :gutter="10">  <!-- Use gutter 10 like original -->
      <!-- CPU -->
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
        <el-popover placement="bottom" :width="loadWidth()" trigger="hover" :disabled="!baseInfo.cpuModelName" popper-class="status-popover cpu-popover">
          <!-- Use el-tag structure based on image -->
          <el-tag class="popover-title-tag cpu-title">{{ baseInfo.cpuModelName || 'N/A' }}</el-tag>
          <div class="cpu-core-info-tags">
            <el-tag>{{ $t('home.physicalCores') }} *{{ baseInfo.cpuCores || 'N/A' }}</el-tag>
            <el-tag>{{ $t('home.logicalCores') }} *{{ baseInfo.cpuLogicalCores || 'N/A' }}</el-tag>
          </div>
          <div class="cpu-core-grid">
            <div v-for="(item, index) of currentInfo.cpuPercent" :key="index">
              <el-tag v-if="cpuShowAll || (!cpuShowAll && index < 32)" class="tag-cpu-class">
                CPU-{{ index }}: {{ formatNumber(item) }}%
              </el-tag>
            </div>
          </div>
          <div v-if="currentInfo.cpuPercent && currentInfo.cpuPercent.length > 32" class="cpu-show-more">
            <!-- Buttons remain -->
            <el-button v-if="!cpuShowAll" @click="cpuShowAll = true" link type="primary" size="small">
              {{ $t('commons.button.showAll') }}
              <el-icon><DArrowRight /></el-icon>
            </el-button>
            <el-button v-if="cpuShowAll" @click="cpuShowAll = false" link type="primary" size="small">
              {{ $t('commons.button.hideSome') }}
              <el-icon><DArrowLeft /></el-icon>
            </el-button>
          </div>
          <template #reference>
            <div ref="cpuChartRef" style="height: 160px; width: 100%; cursor: pointer;"></div>
          </template>
        </el-popover>
        <div class="gauge-bottom-text">CPU 监控</div>
      </el-col>

      <!-- Memory -->
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
        <el-popover placement="bottom" width="auto" trigger="hover" popper-class="status-popover memory-popover">
          <div class="memory-popover-grid">
            <div class="memory-column">
              <el-tag class="popover-title-tag">{{ $t('home.mem') }}:</el-tag>
              <el-tag>{{ $t('home.total') }}: {{ computeSize(currentInfo.memoryTotal) }}</el-tag>
              <el-tag>{{ $t('home.used') }}: {{ computeSize(currentInfo.memoryUsed) }}</el-tag>
              <el-tag>{{ $t('home.free') }}: {{ computeSize(currentInfo.memoryAvailable) }}</el-tag>
              <el-tag>{{ $t('home.percent') }}: {{ formatNumber(currentInfo.memoryUsedPercent) }}%</el-tag>
            </div>
            <div class="memory-column" v-if="currentInfo.swapMemoryTotal">
              <el-tag class="popover-title-tag">{{ $t('home.swapMem') }}:</el-tag>
              <el-tag>{{ $t('home.total') }}: {{ computeSize(currentInfo.swapMemoryTotal) }}</el-tag>
              <el-tag>{{ $t('home.used') }}: {{ computeSize(currentInfo.swapMemoryUsed) }}</el-tag>
              <el-tag>{{ $t('home.free') }}: {{ computeSize(currentInfo.swapMemoryAvailable) }}</el-tag>
              <el-tag>{{ $t('home.percent') }}: {{ formatNumber(currentInfo.swapMemoryUsedPercent) }}%</el-tag>
            </div>
          </div>
          <template #reference>
            <div ref="memoryChartRef" style="height: 160px; width: 100%; cursor: pointer;"></div>
          </template>
        </el-popover>
        <div class="gauge-bottom-text">内存监控</div>
      </el-col>

      <!-- Load -->
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center">
        <el-popover placement="bottom" width="auto" trigger="hover" popper-class="status-popover load-popover">
          <div class="load-popover-content">
            <!-- Use el-tag for load info -->
            <el-tag>{{ $t('home.loadAverage', 1) }}: {{ formatNumber(currentInfo.load1) }}</el-tag>
            <el-tag>{{ $t('home.loadAverage', 5) }}: {{ formatNumber(currentInfo.load5) }}</el-tag>
            <el-tag>{{ $t('home.loadAverage', 15) }}: {{ formatNumber(currentInfo.load15) }}</el-tag>
          </div>
          <template #reference>
            <div ref="loadChartRef" style="height: 160px; width: 100%; cursor: pointer;"></div>
          </template>
        </el-popover>
        <div class="gauge-bottom-text">负载监控</div>
      </el-col>

      <!-- Disks -->
      <template v-for="(item, index) in currentInfo.diskData" :key="'disk-' + index">
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center" v-if="isShow('disk', index)">
          <el-popover placement="bottom" width="auto" trigger="hover" popper-class="status-popover disk-popover">
            <div class="popover-section disk-base-info">
              <el-tag class="popover-title-tag">{{ $t('home.baseInfo') }}:</el-tag>
              <el-tag>{{ $t('home.mount') }}: {{ item.path }}</el-tag>
              <el-tag>{{ $t('commons.table.type') }}: {{ item.type }}</el-tag>
              <el-tag>{{ $t('home.fileSystem') }}: {{ item.device }}</el-tag>
            </div>
            <div class="disk-popover-grid popover-section">
              <div class="disk-column">
                <el-tag class="popover-title-tag">Inode:</el-tag>
                <el-tag>{{ $t('home.total') }}: {{ item.inodesTotal }}</el-tag>
                <el-tag>{{ $t('home.used') }}: {{ item.inodesUsed }}</el-tag>
                <el-tag>{{ $t('home.free') }}: {{ item.inodesFree }}</el-tag>
                <el-tag>{{ $t('home.percent') }}: {{ formatNumber(item.inodesUsedPercent) }}%</el-tag>
              </div>
              <div class="disk-column">
                <el-tag class="popover-title-tag">{{ $t('monitor.disk') }}:</el-tag>
                <el-tag>{{ $t('home.total') }}: {{ computeSize(item.total) }}</el-tag>
                <el-tag>{{ $t('home.used') }}: {{ computeSize(item.used) }}</el-tag>
                <el-tag>{{ $t('home.free') }}: {{ computeSize(item.free) }}</el-tag>
                <el-tag>{{ $t('home.percent') }}: {{ formatNumber(item.usedPercent) }}%</el-tag>
              </div>
            </div>
            <template #reference>
              <div :ref="(el) => { if (el) diskChartRefs[item.path] = el }" style="height: 160px; width: 100%; cursor: pointer;"></div>
            </template>
          </el-popover>
          <div class="gauge-bottom-text">硬盘监控</div>
        </el-col>
      </template>

      <!-- GPUs -->
      <template v-for="(item, index) in currentInfo.gpuData" :key="'gpu-' + index">
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center" v-if="isShow('gpu', index)">
          <el-popover placement="bottom" :width="250" trigger="hover" popper-class="status-popover gpu-popover">
            <div class="gpu-popover-content">
              <!-- Structure already uses el-tag, ensure class is correct -->
              <el-tag class="popover-title-tag gpu-title-tag">{{ item.productName || 'GPU Info' }}:</el-tag>
              <el-tag>{{ $t('monitor.gpuUtil') }}: {{ item.gpuUtil }}</el-tag>
              <el-tag>{{ $t('monitor.temperature') }}: {{ item.temperature?.replaceAll('C', '°C') }} </el-tag>
              <el-tag>{{ $t('monitor.performanceState') }}: {{ item.performanceState }}</el-tag>
              <el-tag>{{ $t('monitor.powerUsage') }}: {{ item.powerUsage }}</el-tag>
              <el-tag>{{ $t('monitor.memoryUsage') }}: {{ item.memoryUsage }}</el-tag>
              <el-tag>{{ $t('monitor.fanSpeed') }}: {{ item.fanSpeed }}</el-tag>
            </div>
            <template #reference>
              <div :ref="(el) => { if (el) gpuChartRefs[item.index] = el }"
                   @click="goGPU"
                   style="height: 160px; width: 100%; cursor: pointer;"></div>
            </template>
          </el-popover>
          <div class="gauge-bottom-text">显卡监控</div>
        </el-col>
      </template>

      <!-- XPUs -->
      <template v-for="(item, index) in currentInfo.xpuData" :key="'xpu-' + index">
        <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" align="center" v-if="isShow('xpu', index)">
          <el-popover placement="bottom" :width="250" trigger="hover" popper-class="status-popover xpu-popover">
            <!-- Update XPU to use el-tags as well for consistency -->
            <div class="xpu-popover-content">
              <el-tag class="popover-title-tag">{{ item.deviceName || 'XPU Info' }}:</el-tag>
              <el-tag>{{ $t('monitor.gpuUtil') }}: {{ item.memoryUtil }}</el-tag>
              <el-tag>{{ $t('monitor.temperature') }}: {{ item.temperature }}</el-tag>
              <el-tag>{{ $t('monitor.powerUsage') }}: {{ item.power }}</el-tag>
              <el-tag>{{ $t('monitor.memoryUsage') }}: {{ item.memoryUsed }}/{{ item.memory }}</el-tag>
            </div>
            <template #reference>
              <div :ref="(el) => { if (el) xpuChartRefs[item.deviceID] = el }" style="height: 160px; width: 100%; cursor: pointer;"></div>
            </template>
          </el-popover>
          <div class="gauge-bottom-text">XPU 监控</div>
        </el-col>
      </template>

      <!-- Show More / Hide Button -->
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" v-if="totalCount > 5" class="show-more-col">
        <el-button v-if="!showMore" link type="primary" @click="changeShowMore(true)" class="show-more-button">
          {{ $t('tabs.more') }}
          <el-icon><Bottom /></el-icon>
        </el-button>
        <el-button v-if="showMore" type="primary" link @click="changeShowMore(false)" class="show-more-button">
          {{ $t('tabs.hide') }}
          <el-icon><Top /></el-icon>
        </el-button>
      </el-col>
    </el-row>

    <!-- Remove original tables -->
    <!-- <el-row :gutter="20" style="margin-top: 20px" v-if="currentInfo.gpuData && currentInfo.gpuData.length > 0"> ... </el-row> -->
    <!-- <el-row :gutter="20" style="margin-top: 20px" v-if="currentInfo.xpuData && currentInfo.xpuData.length > 0"> ... </el-row> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import { Dashboard } from '@/interface/dashboard';
import { computeSize } from '@/utils/util';
import i18n from '@/i18n';
import { useRouter } from 'vue-router';
// Import icons if not globally registered
import { DArrowRight, DArrowLeft, Bottom, Top } from '@element-plus/icons-vue';

const router = useRouter();

// --- Refs for Chart Elements and Instances ---
const cpuChartRef = ref<HTMLElement | null>(null);
const memoryChartRef = ref<HTMLElement | null>(null);
const loadChartRef = ref<HTMLElement | null>(null);
const diskChartRefs = ref<{ [key: string]: any | null }>({});
const gpuChartRefs = ref<{ [key: number]: any | null }>({});
const xpuChartRefs = ref<{ [key: number]: any | null }>({}); // Add refs for XPU charts
const chartInstances = ref<{ [key: string]: echarts.ECharts | null }>({});

// --- State from Original Code ---
const cpuShowAll = ref(false);
const showMore = ref(false);
const totalCount = ref(0);

// --- Reactive Data (CurrentInfo, BaseInfo) - Keep existing ---
const currentInfo = ref<Dashboard.CurrentInfo>({
  hostId: '',
  uptime: 0,
  timeSinceUptime: '',
  procs: 0,
  load1: 0,
  load5: 0,
  load15: 0,
  loadUsagePercent: 0,
  cpuPercent: [] as Array<number>,
  cpuUsedPercent: 0,
  cpuUsed: 0,
  cpuTotal: 0,
  memoryTotal: 0,
  memoryAvailable: 0,
  memoryUsed: 0,
  memoryUsedPercent: 0,
  swapMemoryTotal: 0,
  swapMemoryAvailable: 0,
  swapMemoryUsed: 0,
  swapMemoryUsedPercent: 0,
  ioReadBytes: 0,
  ioWriteBytes: 0,
  ioCount: 0,
  ioReadTime: 0,
  ioWriteTime: 0,
  diskData: [],
  gpuData: [],
  xpuData: [],
  netBytesSent: 0,
  netBytesRecv: 0,
  shotTime: 0,
});

const baseInfo = ref<Dashboard.BaseInfo>({
  websiteNumber: 0,
  databaseNumber: 0,
  cronjobNumber: 0,
  appInstalledNumber: 0,
  hostId: '',
  hostname: '',
  os: '',
  platform: '',
  platformFamily: '',
  platformVersion: '',
  kernelArch: '',
  kernelVersion: '',
  virtualizationSystem: '',
  ipv4Addr: '',
  systemProxy: '',
  cpuCores: 0,
  cpuLogicalCores: 0,
  cpuModelName: '',
  currentInfo: {} as Dashboard.CurrentInfo,
});

// --- Helper Functions ---

const parsePercentString = (percentString: string | undefined | null): number => {
  if (!percentString) return 0;
  const match = percentString.match(/(\d+(\.\d+)?)/);
  return match ? parseFloat(match[1]) : 0;
};

function formatNumber(num: number | undefined | null): number {
  return Number((num || 0).toFixed(2));
}

// Function to determine load status text (already exists)
function loadStatus(val: number | undefined | null): string {
  const percent = val || 0;
  if (percent < 30) {
    return i18n.global.t('home.runSmoothly');
  }
  if (percent < 70) {
    return i18n.global.t('home.runNormal');
  }
  if (percent < 80) {
    return i18n.global.t('home.runSlowly');
  }
  return i18n.global.t('home.runJam');
}

// --- ECharts Option Creation (Refined) ---
const createGaugeOption = (value: number, name: string) => {
  return {
    series: [
      {
        type: 'gauge',
        center: ['50%', '60%'],
        radius: '90%',
        startAngle: 200,
        endAngle: -20,
        min: 0,
        max: 100,
        splitNumber: 5,
        itemStyle: {
          color: '#3a9ff5'
        },
        progress: {
          show: true,
          width: 10 // Thinner progress line
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 10 // Thinner axis line
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '0%'], // Center detail vertically
          fontSize: 22, // Slightly larger font
          fontWeight: 'normal', // Normal weight
          formatter: '{value}%',
          color: 'inherit'
        },
        data: [
          {
            value: value,
            name: name // Add name to display as title
          }
        ]
      }
    ]
  };
};

// --- Computed Properties & Logic from Original ---

// Computed property for display disks (adjust if needed, original didn't filter)
// const displayDisks = computed(() => {
//     return (currentInfo.value.diskData || []); //.filter(disk => ['/', '/home'].includes(disk.path));
// });

// Recalculate total count for show/hide logic
const updateTotalCount = () => {
  totalCount.value = (currentInfo.value.diskData?.length || 0) +
    (currentInfo.value.gpuData?.length || 0) +
    (currentInfo.value.xpuData?.length || 0);
};

const isShow = (val: string, index: number) => {
  let showCount = totalCount.value < 6 ? 5 : 4; // Original logic had 5 items visible initially if total < 6
  switch (val) {
    case 'disk':
      return showMore.value || index < showCount;
    case 'gpu':
      let gpuCount = showCount - (currentInfo.value.diskData?.length || 0);
      return showMore.value || index < gpuCount;
    case 'xpu':
      let xpuCount = showCount - (currentInfo.value.diskData?.length || 0) - (currentInfo.value.gpuData?.length || 0);
      return showMore.value || index < xpuCount;
  }
  return false; // Default case
};

const changeShowMore = (show: boolean) => {
  showMore.value = show;
  localStorage.setItem('dashboard_show', show ? 'more' : 'hide');
};

// Calculate popover width based on CPU cores
const loadWidth = () => {
  if (!cpuShowAll.value || !currentInfo.value.cpuPercent || currentInfo.value.cpuPercent.length < 32) {
    return 310; // Default width from original
  }
  let columns = 2;
  let tagWidth = 140; // Approx width per tag
  let gap = 5;
  // Calculate how many tags per column based on display logic (e.g., 16 per column if > 32)
  let tagsPerColumn = Math.ceil(currentInfo.value.cpuPercent.length / columns);
  // This width calculation needs refinement based on exact layout/styling
  // Original calculation might have been different
  // return tagsPerColumn * (tagWidth + gap) + 20; // Example width logic
  return Math.max(310, columns * (tagWidth + gap*2) + 20); // Simplified width, ensure minimum
};

// --- Chart Update Logic ---
const updateOrCreateChart = (key: string, option: any, chartRefElement: HTMLElement | null) => {
  if (!chartRefElement) {
    return;
  }
  try {
    let chart = chartInstances.value[key];
    
    // 如果已有实例，尝试设置选项
    if (chart) {
      try {
        chart.setOption(option, true);
      } catch (e) {
        // 如果设置失败，可能是实例已损坏，尝试重新创建
        console.warn(`重设图表 ${key} 选项失败，尝试重新创建`);
        chart.dispose();
        chartInstances.value[key] = null;
        chart = null;
      }
    }
    
    // 创建新实例
    if (!chart) {
      // 检查DOM元素尺寸
      if (!chartRefElement.clientWidth || !chartRefElement.clientHeight) {
        console.warn(`Chart element for key '${key}' has no dimensions yet. Will retry later.`);
        // 添加延迟重试
        setTimeout(() => {
          if (chartRefElement.clientWidth && chartRefElement.clientHeight) {
            try {
              const newChart = echarts.init(chartRefElement);
              newChart.setOption(option);
              chartInstances.value[key] = newChart;
            } catch (err) {
              console.error(`Delayed chart init for '${key}' failed:`, err);
            }
          }
        }, 100); // 100ms后重试
        return;
      }
      
      // 确保没有遗留的实例
      const existingInstance = echarts.getInstanceByDom(chartRefElement);
      if (existingInstance) {
        existingInstance.dispose();
      }
      
      // 创建新实例
      chart = echarts.init(chartRefElement);
      chart.setOption(option);
      chartInstances.value[key] = chart;
    }
  } catch (error) {
    console.error(`Failed to update/create chart '${key}':`, error);
    if (chartInstances.value[key]) {
      chartInstances.value[key]?.dispose();
      chartInstances.value[key] = null;
    }
  }
};

// --- Data Reception & Processing ---
const acceptParams = (current: Dashboard.CurrentInfo, base: Dashboard.BaseInfo): void => {
  if (!current || !base) {
    console.warn('Status组件接收到无效的数据');
    return;
  }

  try {
    // Ensure arrays are always arrays before merging
    current.diskData = Array.isArray(current.diskData) ? current.diskData : [];
    current.gpuData = Array.isArray(current.gpuData) ? current.gpuData : [];
    current.xpuData = Array.isArray(current.xpuData) ? current.xpuData : [];
    current.cpuPercent = Array.isArray(current.cpuPercent) ? current.cpuPercent : [];

    // Merge data
    currentInfo.value = { ...currentInfo.value, ...current };
    baseInfo.value = { ...baseInfo.value, ...base };

    // Recalculate total count after data update
    updateTotalCount();
    // Load showMore state
    showMore.value = localStorage.getItem('dashboard_show') === 'more';

    // Calculate percentages
    const cpuUsedPercent = formatNumber(currentInfo.value.cpuUsedPercent || 0);
    const memoryUsedPercent = formatNumber(currentInfo.value.memoryUsedPercent || 0);
    const loadUsagePercent = formatNumber(currentInfo.value.loadUsagePercent || 0);

    // Generate options with Chinese labels
    const cpuOption = createGaugeOption(cpuUsedPercent, 'CPU 监控');
    const memoryOption = createGaugeOption(memoryUsedPercent, '内存监控');
    const loadOption = createGaugeOption(loadUsagePercent, '负载监控');

    const diskOptionsMap: { [key: string]: any } = {};
    (currentInfo.value.diskData || []).forEach(disk => {
      const diskUsedPercent = formatNumber(disk.usedPercent || 0);
      const diskName = `硬盘监控`;
      diskOptionsMap[disk.path] = createGaugeOption(diskUsedPercent, diskName);
    });

    const gpuOptionsMap: { [key: number]: any } = {};
    (currentInfo.value.gpuData || []).forEach(gpu => {
      const gpuUtilValue = parsePercentString(gpu.gpuUtil);
      const gpuName = `显卡监控`;
      gpuOptionsMap[gpu.index] = createGaugeOption(gpuUtilValue, gpuName);
    });

    const xpuOptionsMap: { [key: number]: any } = {}; // Add XPU map
    (currentInfo.value.xpuData || []).forEach(xpu => {
      const xpuUtilValue = parsePercentString(xpu.memoryUtil); // Assuming memoryUtil for XPU gauge
      const xpuName = `XPU 监控`;
      xpuOptionsMap[xpu.deviceID] = createGaugeOption(xpuUtilValue, xpuName);
    });

    // Update charts using nextTick
    nextTick(() => {
      updateOrCreateChart('cpu', cpuOption, cpuChartRef.value);
      updateOrCreateChart('memory', memoryOption, memoryChartRef.value);
      updateOrCreateChart('load', loadOption, loadChartRef.value);

      // --- Update Disk Charts ---
      const currentDiskKeys = new Set(Object.keys(diskOptionsMap));
      // Update existing / Create new
      Object.keys(diskOptionsMap).forEach(path => {
        updateOrCreateChart(`disk-${path}`, diskOptionsMap[path], diskChartRefs.value[path]);
      });
      // Dispose old
      Object.keys(diskChartRefs.value).forEach(path => {
        if (!currentDiskKeys.has(path)) {
          chartInstances.value[`disk-${path}`]?.dispose();
          delete chartInstances.value[`disk-${path}`];
          delete diskChartRefs.value[path]; // Remove ref
        }
      });

      // --- Update GPU Charts ---
      const currentGpuKeys = new Set(Object.keys(gpuOptionsMap).map(Number));
      // Update existing / Create new
      Object.keys(gpuOptionsMap).map(Number).forEach(index => {
        updateOrCreateChart(`gpu-${index}`, gpuOptionsMap[index], gpuChartRefs.value[index]);
      });
      // Dispose old
      Object.keys(gpuChartRefs.value).map(Number).forEach(index => {
        if (!currentGpuKeys.has(index)) {
          chartInstances.value[`gpu-${index}`]?.dispose();
          delete chartInstances.value[`gpu-${index}`];
          delete gpuChartRefs.value[index]; // Remove ref
        }
      });

      // --- Update XPU Charts ---
      const currentXpuKeys = new Set(Object.keys(xpuOptionsMap).map(Number));
      // Update existing / Create new
      Object.keys(xpuOptionsMap).map(Number).forEach(deviceID => {
        updateOrCreateChart(`xpu-${deviceID}`, xpuOptionsMap[deviceID], xpuChartRefs.value[deviceID]);
      });
      // Dispose old
      Object.keys(xpuChartRefs.value).map(Number).forEach(deviceID => {
        if (!currentXpuKeys.has(deviceID)) {
          chartInstances.value[`xpu-${deviceID}`]?.dispose();
          delete chartInstances.value[`xpu-${deviceID}`];
          delete xpuChartRefs.value[deviceID]; // Remove ref
        }
      });
    });

  } catch (error) {
    console.error('状态组件更新失败:', error);
  }
};

// --- Navigation Function ---
const goGPU = () => {
  if (currentInfo.value.hostId) {
    router.push({ path: '/gpu', query: { hostId: currentInfo.value.hostId } });
  } else {
    console.warn('无法获取当前 hostId，无法跳转到指定主机的 GPU 页面');
    router.push({ path: '/gpu' }); // Fallback: Navigate without hostId
  }
};

// --- Lifecycle Hooks ---
const handleResize = () => {
  Object.values(chartInstances.value).forEach(chart => chart?.resize());
};

const disposeCharts = () => {
  try {
    Object.entries(chartInstances.value).forEach(([key, chart]) => {
      if (chart) {
        try {
          chart.dispose();
        } catch (e) {
          console.warn(`Disposing chart ${key} failed`, e);
        }
      }
    });
    chartInstances.value = {};
    
    // 清除所有引用
    diskChartRefs.value = {};
    gpuChartRefs.value = {};
    xpuChartRefs.value = {};
  } catch (e) {
    console.error('Error during charts disposal', e);
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  // Optionally load showMore state on mount
  showMore.value = localStorage.getItem('dashboard_show') === 'more';
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  disposeCharts();
});

defineExpose({
  acceptParams,
});

defineOptions({
  name: 'StatusComponent'
});
</script>

<style>
/* Popover Base Styles */
.status-popover {
  font-size: 12px !important; /* Ensure base font size */
  padding: 8px 12px !important; /* Match original style */
  line-height: 1.7;
  max-width: 450px;
  background-color: #fff; /* Ensure background */
  border: 1px solid var(--el-border-color-lighter);
  box-shadow: var(--el-box-shadow-light);
  color: var(--el-text-color-regular);
}

/* --- General Popover Tag Styles (Based on images) --- */
.status-popover .el-tag {
  display: block; /* Tags take full width */
  width: 100%;
  margin-bottom: 4px; /* Space between tags */
  justify-content: flex-start; /* Align text left */
  height: 26px; /* Consistent height */
  line-height: 24px;
  border-radius: 4px;
  font-size: 12px;
  /* Use specific hex codes for closer match to image */
  background-color: var(--el-color-primary-light-9); /* #ecf5ff */
  border-color: var(--el-color-primary-light-8); /* #d9ecff */
  color: var(--el-color-primary); /* #409eff */
  padding: 0 8px; /* Adjust padding */
}
.status-popover .el-tag:last-child {
  margin-bottom: 0;
}

/* Style for Title Tags */
.status-popover .popover-title-tag {
  font-weight: 500 !important;
  background-color: transparent !important;
  border: none !important;
  padding-left: 0 !important;
  margin-bottom: 5px; /* Add space below title */
  height: auto; /* Allow title height to adjust */
  line-height: normal;
}

.popover-section {
  margin-bottom: 8px;
}
.popover-section:last-child {
  margin-bottom: 0;
}

/* CPU Popover */
.cpu-popover .cpu-title {
  font-weight: 500;
  margin-bottom: 5px;
  word-break: break-all; /* Allow long model names to wrap */
  white-space: normal; /* Ensure wrapping for title tag */
}
.cpu-popover .cpu-core-info-tags {
  display: flex;
  gap: 10px;
}
.cpu-popover .cpu-core-info-tags .el-tag {
  width: auto; /* Let tags size to content */
  display: inline-block;
}
.cpu-popover .cpu-core-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); /* Adjust min width */
  gap: 4px 8px;
  margin-top: 8px;
  max-height: 180px; /* Keep max height */
  overflow-y: auto;
}
.cpu-popover .tag-cpu-class {
  margin: 0;
  padding: 2px 5px;
  height: 24px; /* Slightly shorter for grid */
  line-height: normal; /* Adjust line height for 24px height */
  width: 100%;
  justify-content: flex-start;
  /* Inherit general tag colors but keep specific grid layout styles */
  background-color: var(--el-color-primary-light-9); /* Use the same lighter blue */
  border-color: var(--el-color-primary-light-8); /* Use the same lighter border */
  color: var(--el-color-primary); /* Use the same blue text */
  border-width: 1px; /* Ensure border width is applied */
  border-style: solid;
  border-radius: 4px; /* Match general tag radius */
  font-size: 11px; /* Keep smaller font for grid */
}
.cpu-popover .cpu-show-more {
  margin-top: 8px;
  text-align: right;
}

/* Memory Popover */
.memory-popover .memory-popover-grid {
  display: flex; /* Use flex for side-by-side */
  gap: 20px; /* Space between columns */
}
.memory-popover .memory-column .el-tag {
  display: block; /* Ensure tags are block level */
}

/* Load Popover */
.load-popover .load-popover-content .el-tag {
  /* Use general tag styles */
}

/* Disk Popover */
.disk-popover .disk-base-info .el-tag {
  /* Use general tag styles */
}
.disk-popover .disk-popover-grid {
  display: flex;
  gap: 30px; /* Increase gap between Inode and Disk columns */
  margin-top: 8px;
}
.disk-popover .disk-column .el-tag {
  display: block; /* Ensure tags are block level */
  white-space: nowrap; /* Prevent wrapping in disk columns */
}

/* GPU/XPU Popover */
.gpu-popover .gpu-popover-content .el-tag,
.xpu-popover .xpu-popover-content .el-tag {
  display: block; /* Make tags block level */
  margin-bottom: 4px; /* Add space between tags */
  width: 100%; /* Make tags fill width */
  justify-content: flex-start; /* Align text left */
  height: 26px; /* Slightly taller tags */
  line-height: 24px;
}
.gpu-popover .gpu-title-tag,
.xpu-popover .popover-title-tag {
  font-weight: 500; /* Make title tag bold */
  background-color: transparent !important; /* Remove background for title */
  border: none !important; /* Remove border for title */
  padding-left: 0 !important; /* Remove padding for title */
}

/* General Gauge Styles */
.gauge-bottom-text {
  text-align: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: -15px; /* Adjust vertical position */
  margin-bottom: 15px; /* Add more space below */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 5px; /* Add padding to prevent text touching edges */
}

/* Show More Button Column */
.show-more-col {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160px;
  margin-top: 10px;
}
.show-more-button {
  font-size: 12px;
}
</style>
