pipeline {
    agent any
    stages {

        stage('pull code') {
            steps {
                git branch: 'dev_main', credentialsId: 'gitlab-jihu', url: 'http://************:30080/ucas-unicorn-2023/edge-computing/edge-computing-admin-ui.git'
                sh "ls -la"
                echo '拉取完成'
            }
        }

        stage('docker build') {
            steps {
                sh "docker build -f Dockerfile -t ccr.ccs.tencentyun.com/game-center/edge-computing-admin-ui:prod_${BUILD_ID} ."
                echo '构建完成'
            }
        }

        stage('docker push') {
            steps {
                sh 'docker image prune -f'

                sh "docker login ccr.ccs.tencentyun.com --username=100010627116 -p mustang123456"
                sh "docker push ccr.ccs.tencentyun.com/game-center/edge-computing-admin-ui:prod_${BUILD_ID}"
                echo '推送完成'

                sh "docker rmi -f ccr.ccs.tencentyun.com/game-center/edge-computing-admin-ui:prod_${BUILD_ID}"
                sh 'docker image prune -f'
                echo '清除镜像完成'
            }
        }

        stage('Deploy to k8s cluster') {
            steps {
                rancherRedeploy(
                    workload: '/project/c-tr9hl:p-pfv6s/workloads/deployment:edge:edge-computing-admin-ui',
                    credential: 'rancher2_ucas',
                    alwaysPull: true,
                    images: 'ccr.ccs.tencentyun.com/game-center/edge-computing-admin-ui:prod_${BUILD_ID}'
                )
                echo 'Deploy 部署完成'
            }
        }

    }
}
